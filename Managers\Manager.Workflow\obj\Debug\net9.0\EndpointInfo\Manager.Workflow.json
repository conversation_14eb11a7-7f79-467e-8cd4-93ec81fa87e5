{"openapi": "3.0.1", "info": {"title": "Manager.Workflow", "version": "1.0"}, "paths": {"/api/Workflow": {"get": {"tags": ["Workflow"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowEntity"}}}}}}}, "post": {"tags": ["Workflow"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowEntity"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WorkflowEntity"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkflowEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowEntity"}}}}}}}, "/api/Workflow/paged": {"get": {"tags": ["Workflow"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK"}}}}, "/api/Workflow/{id}": {"get": {"tags": ["Workflow"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkflowEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowEntity"}}}}}}, "put": {"tags": ["Workflow"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowEntity"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/WorkflowEntity"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkflowEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowEntity"}}}}}}, "delete": {"tags": ["Workflow"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Workflow/composite/{version}/{name}": {"get": {"tags": ["Workflow"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkflowEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowEntity"}}}}}}}, "/api/Workflow/step/{stepId}": {"get": {"tags": ["Workflow"], "parameters": [{"name": "stepId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowEntity"}}}}}}}}, "/api/Workflow/step/{stepId}/exists": {"get": {"tags": ["Workflow"], "parameters": [{"name": "stepId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/Workflow/version/{version}": {"get": {"tags": ["Workflow"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowEntity"}}}}}}}}, "/api/Workflow/name/{name}": {"get": {"tags": ["Workflow"], "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowEntity"}}}}}}}}, "/api/Workflow/composite": {"get": {"tags": ["Workflow"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/WorkflowEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/WorkflowEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/WorkflowEntity"}}}}}}}}, "components": {"schemas": {"WorkflowEntity": {"required": ["name", "stepIds", "version"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "version": {"maxLength": 50, "minLength": 0, "pattern": "^\\d+\\.\\d+\\.\\d+$", "type": "string"}, "name": {"maxLength": 50, "minLength": 0, "type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "nullable": true}, "updatedBy": {"type": "string", "nullable": true}, "description": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}, "isNew": {"type": "boolean", "readOnly": true}, "stepIds": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, "additionalProperties": false}}}}