<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>Shared.Processor</RootNamespace>
    <AssemblyName>Shared.Processor</AssemblyName>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
    <Authors>Manager.Schemas Team</Authors>
    <Description>Shared processor classes and base application framework for the Manager.Schemas application</Description>
    <PackageId>Shared.Processor</PackageId>
    <Version>1.0.0</Version>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="9.0.0" />
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.5" />
    <PackageReference Include="System.Diagnostics.DiagnosticSource" Version="9.0.0" />
    <PackageReference Include="MassTransit" Version="8.2.5" />
    <PackageReference Include="MassTransit.RabbitMQ" Version="8.2.5" />
    <PackageReference Include="Hazelcast.Net" Version="5.5.0" />
    <PackageReference Include="OpenTelemetry" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.12.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Newtonsoft.Json.Schema" Version="4.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Shared.Correlation\Shared.Correlation.csproj" />
    <ProjectReference Include="..\Shared.Entities\Shared.Entities.csproj" />
    <ProjectReference Include="..\Shared.Extensions\Shared.Extensions.csproj" />
    <ProjectReference Include="..\Shared.Models\Shared.Models.csproj" />
    <ProjectReference Include="..\Shared.Services\Shared.Services.csproj" />
    <ProjectReference Include="..\Shared.Configuration\Shared.Configuration.csproj" />
    <ProjectReference Include="..\Shared.MassTransit\Shared.MassTransit.csproj" />
  </ItemGroup>

</Project>
