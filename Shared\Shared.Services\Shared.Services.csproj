<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>Shared.Services</RootNamespace>
    <AssemblyName>Shared.Services</AssemblyName>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
    <Authors>Manager.Schemas Team</Authors>
    <Description>Shared service classes for the Manager.Schemas application</Description>
    <PackageId>Shared.Services</PackageId>
    <Version>1.0.0</Version>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MassTransit" Version="8.2.5" />
    <PackageReference Include="Hazelcast.Net" Version="5.5.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="8.0.0" />
    <PackageReference Include="System.Diagnostics.DiagnosticSource" Version="8.0.1" />
    <PackageReference Include="System.Text.Json" Version="9.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Shared.Correlation\Shared.Correlation.csproj" />
    <ProjectReference Include="..\Shared.Extensions\Shared.Extensions.csproj" />
    <ProjectReference Include="..\Shared.Models\Shared.Models.csproj" />
  </ItemGroup>

</Project>
