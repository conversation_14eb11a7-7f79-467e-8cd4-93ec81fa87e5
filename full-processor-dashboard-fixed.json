{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"id": 1, "title": "Core Health & Status Metrics", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "collapsed": false}, {"id": 2, "title": "Processor Health Status", "description": "Current health status of the processor. Values: 0=Healthy (Green), 1=Degraded (Yellow), 2=Unhealthy (Red).", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "processor_health_status_Current_health_status_of_the_processor_0_Healthy_1_Degraded_2_Unhealthy{processor_composite_key=~\"$processor_composite_key\"}", "instant": false, "legendFormat": "{{processor_composite_key}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1}, {"color": "red", "value": 2}]}, "mappings": [{"options": {"0": {"text": "Healthy"}, "1": {"text": "Degraded"}, "2": {"text": "Unhealthy"}}, "type": "value"}]}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 1}}, {"id": 3, "title": "Processor Uptime", "description": "Total uptime of the processor in seconds since last restart.", "type": "stat", "targets": [{"datasource": {"type": "prometheus", "uid": "e98bd485-7d85-41e3-a6d2-3e2dc109e011"}, "editorMode": "code", "expr": "processor_uptime_seconds_Total_uptime_of_the_processor_in_seconds{processor_composite_key=~\"$processor_composite_key\"}", "instant": false, "legendFormat": "{{processor_composite_key}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "s", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 1}}, {"id": 4, "title": "Performance Metrics", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "collapsed": false}, {"id": 5, "title": "CPU Usage", "description": "Current CPU usage percentage (0-100) of the processor.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus", "uid": "e98bd485-7d85-41e3-a6d2-3e2dc109e011"}, "editorMode": "code", "expr": "processor_cpu_usage_percent_Current_CPU_usage_percentage_0_100{processor_composite_key=~\"$processor_composite_key\"}", "instant": false, "legendFormat": "{{processor_composite_key}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "color": {"mode": "palette-classic"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 70}, {"color": "red", "value": 90}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}}, {"id": 6, "title": "Memory Usage", "description": "Current memory usage in bytes of the processor.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus", "uid": "e98bd485-7d85-41e3-a6d2-3e2dc109e011"}, "editorMode": "code", "expr": "processor_memory_usage_bytes_Current_memory_usage_in_bytes{processor_composite_key=~\"$processor_composite_key\"}", "instant": false, "legendFormat": "{{processor_composite_key}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "bytes", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}}, {"id": 7, "title": "Activity Processing Metrics", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "collapsed": false}, {"id": 8, "title": "Activities Processed (Total)", "description": "Total number of activities processed (aggregated).", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus", "uid": "e98bd485-7d85-41e3-a6d2-3e2dc109e011"}, "editorMode": "code", "expr": "sum(processor_activities_processed_Total_number_of_activities_processed_total{processor_composite_key=~\"$processor_composite_key\"})", "instant": false, "legendFormat": "{{processor_composite_key}} Total", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 19}}, {"id": 9, "title": "Activities Total", "description": "Total number of activities processed since startup.", "type": "stat", "targets": [{"datasource": {"type": "prometheus", "uid": "e98bd485-7d85-41e3-a6d2-3e2dc109e011"}, "editorMode": "code", "expr": "processor_activities_Total_number_of_activities_processed_since_startup_total{processor_composite_key=~\"$processor_composite_key\"}", "instant": false, "legendFormat": "{{processor_composite_key}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 19}}, {"id": 10, "title": "Activities Successful", "description": "Total number of activities that completed successfully.", "type": "stat", "targets": [{"datasource": {"type": "prometheus", "uid": "e98bd485-7d85-41e3-a6d2-3e2dc109e011"}, "editorMode": "code", "expr": "processor_activities_successful_Total_number_of_activities_that_completed_successfully_total{processor_composite_key=~\"$processor_composite_key\"}", "instant": false, "legendFormat": "{{processor_composite_key}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 19}}, {"id": 18, "title": "Activity Success Rate", "description": "Success rate percentage of activities processed (successful vs total).", "type": "stat", "targets": [{"datasource": {"type": "prometheus", "uid": "e98bd485-7d85-41e3-a6d2-3e2dc109e011"}, "editorMode": "code", "expr": "(sum(processor_activities_successful_Total_number_of_activities_that_completed_successfully_total{processor_composite_key=~\"$processor_composite_key\"}) / sum(processor_activities_Total_number_of_activities_processed_since_startup_total{processor_composite_key=~\"$processor_composite_key\"})) * 100", "instant": false, "legendFormat": "{{processor_composite_key}} Success Rate", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}, "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 27}}, {"id": 19, "title": "Activity Processing Trend", "description": "Trend of activity processing over time (aggregated by status).", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus", "uid": "e98bd485-7d85-41e3-a6d2-3e2dc109e011"}, "editorMode": "code", "expr": "sum by (status) (rate(processor_activities_processed_Total_number_of_activities_processed_total{processor_composite_key=~\"$processor_composite_key\"}[5m]))", "instant": false, "legendFormat": "{{status}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 16, "x": 8, "y": 27}}, {"id": 20, "title": "Activity Duration Distribution", "description": "Histogram showing the distribution of activity processing durations in seconds.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus", "uid": "e98bd485-7d85-41e3-a6d2-3e2dc109e011"}, "editorMode": "code", "expr": "sum by (le) (rate(processor_activity_duration_seconds_Duration_of_activity_processing_in_seconds_bucket{processor_composite_key=~\"$processor_composite_key\"}[5m]))", "instant": false, "legendFormat": "≤ {{le}}s", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops", "color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "stepAfter", "fillOpacity": 10}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 35}}, {"id": 21, "title": "Activity Execution Time Distribution", "description": "Histogram showing the distribution of activity execution times in seconds.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus", "uid": "e98bd485-7d85-41e3-a6d2-3e2dc109e011"}, "editorMode": "code", "expr": "sum by (le) (rate(processor_activity_execution_time_seconds_Distribution_of_activity_execution_times_in_seconds_bucket{processor_composite_key=~\"$processor_composite_key\"}[5m]))", "instant": false, "legendFormat": "≤ {{le}}s", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops", "color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "stepAfter", "fillOpacity": 10}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 35}}, {"id": 22, "title": "Average Activity Duration", "description": "Average duration of activity processing in seconds.", "type": "stat", "targets": [{"datasource": {"type": "prometheus", "uid": "e98bd485-7d85-41e3-a6d2-3e2dc109e011"}, "editorMode": "code", "expr": "sum(rate(processor_activity_duration_seconds_Duration_of_activity_processing_in_seconds_sum{processor_composite_key=~\"$processor_composite_key\"}[5m])) / sum(rate(processor_activity_duration_seconds_Duration_of_activity_processing_in_seconds_count{processor_composite_key=~\"$processor_composite_key\"}[5m]))", "instant": false, "legendFormat": "Avg Duration", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "s", "color": {"mode": "palette-classic"}, "decimals": 3}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 43}}, {"id": 23, "title": "Average Execution Time", "description": "Average execution time of activities in seconds.", "type": "stat", "targets": [{"datasource": {"type": "prometheus", "uid": "e98bd485-7d85-41e3-a6d2-3e2dc109e011"}, "editorMode": "code", "expr": "sum(rate(processor_activity_execution_time_seconds_Distribution_of_activity_execution_times_in_seconds_sum{processor_composite_key=~\"$processor_composite_key\"}[5m])) / sum(rate(processor_activity_execution_time_seconds_Distribution_of_activity_execution_times_in_seconds_count{processor_composite_key=~\"$processor_composite_key\"}[5m]))", "instant": false, "legendFormat": "Avg Execution Time", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "s", "color": {"mode": "palette-classic"}, "decimals": 3}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 43}}, {"id": 24, "title": "Duration Percentiles", "description": "95th and 99th percentile of activity processing duration.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus", "uid": "e98bd485-7d85-41e3-a6d2-3e2dc109e011"}, "editorMode": "code", "expr": "histogram_quantile(0.95, sum by (le) (rate(processor_activity_duration_seconds_Duration_of_activity_processing_in_seconds_bucket{processor_composite_key=~\"$processor_composite_key\"}[5m])))", "instant": false, "legendFormat": "95th percentile", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "e98bd485-7d85-41e3-a6d2-3e2dc109e011"}, "editorMode": "code", "expr": "histogram_quantile(0.99, sum by (le) (rate(processor_activity_duration_seconds_Duration_of_activity_processing_in_seconds_bucket{processor_composite_key=~\"$processor_composite_key\"}[5m])))", "instant": false, "legendFormat": "99th percentile", "range": true, "refId": "B"}], "fieldConfig": {"defaults": {"unit": "s", "color": {"mode": "palette-classic"}, "decimals": 3}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 43}}, {"id": 11, "title": "File Processing Metrics", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 35}, "collapsed": false}, {"id": 12, "title": "Files Processed (Rate)", "description": "Rate of files processed per second by file type.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus", "uid": "e98bd485-7d85-41e3-a6d2-3e2dc109e011"}, "editorMode": "code", "expr": "sum by (processor_composite_key, file_type) (rate(processor_files_processed_Total_number_of_files_processed_by_the_processor_total{processor_composite_key=~\"$processor_composite_key\"}[5m]))", "instant": false, "legendFormat": "{{processor_composite_key}} - {{file_type}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 51}}, {"id": 13, "title": "Files Successful (Rate)", "description": "Rate of files successfully processed per second.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus", "uid": "e98bd485-7d85-41e3-a6d2-3e2dc109e011"}, "editorMode": "code", "expr": "sum by (processor_composite_key, file_type) (rate(processor_files_successful_Total_number_of_files_successfully_processed_total{processor_composite_key=~\"$processor_composite_key\"}[5m]))", "instant": false, "legendFormat": "{{processor_composite_key}} - {{file_type}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 51}}, {"id": 14, "title": "Cache & Health Metrics", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 59}, "collapsed": false}, {"id": 15, "title": "Cache Active Entries", "description": "Total number of active cache entries.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus", "uid": "e98bd485-7d85-41e3-a6d2-3e2dc109e011"}, "editorMode": "code", "expr": "processor_cache_active_entries_total_Total_number_of_active_cache_entries{processor_composite_key=~\"$processor_composite_key\"}", "instant": false, "legendFormat": "{{processor_composite_key}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 60}}, {"id": 16, "title": "Cache Entry Age", "description": "Average age of cache entries in seconds.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus", "uid": "e98bd485-7d85-41e3-a6d2-3e2dc109e011"}, "editorMode": "code", "expr": "processor_cache_average_entry_age_seconds_Average_age_of_cache_entries_in_seconds{processor_composite_key=~\"$processor_composite_key\"}", "instant": false, "legendFormat": "{{processor_composite_key}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "s", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 60}}, {"id": 17, "title": "Health Checks (Rate)", "description": "Rate of health checks performed per second by type and status.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus", "uid": "e98bd485-7d85-41e3-a6d2-3e2dc109e011"}, "editorMode": "code", "expr": "sum by (processor_composite_key, health_check_name, health_check_status) (rate(processor_health_checks_Total_number_of_health_checks_performed_total{processor_composite_key=~\"$processor_composite_key\"}[5m]))", "instant": false, "legendFormat": "{{processor_composite_key}} - {{health_check_name}} ({{health_check_status}})", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 60}}], "refresh": "30s", "schemaVersion": 38, "style": "dark", "tags": ["processor", "metrics", "monitoring"], "templating": {"list": [{"name": "processor_composite_key", "type": "query", "label": "Processor", "description": "Select one or more processors to monitor", "query": "label_values(processor_health_status_Current_health_status_of_the_processor_0_Healthy_1_Degraded_2_Unhealthy, processor_composite_key)", "datasource": {"type": "prometheus", "uid": "e98bd485-7d85-41e3-a6d2-3e2dc109e011"}, "refresh": 1, "sort": 1, "multi": true, "includeAll": true, "allValue": ".*", "current": {"selected": true, "text": "All", "value": "$__all"}, "options": [], "regex": "", "skipUrlSync": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "Processor Metrics Dashboard - Complete", "uid": null, "version": 1, "weekStart": ""}