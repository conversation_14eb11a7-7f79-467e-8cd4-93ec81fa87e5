# Prometheus configuration for Design27 observability stack
global:
  scrape_interval: 15s
  evaluation_interval: 15s

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

# A scrape configuration containing exactly one endpoint to scrape:
scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # OpenTelemetry Collector metrics
  - job_name: 'otel-collector'
    static_configs:
      - targets: ['otel-collector:8888']
    scrape_interval: 10s
    metrics_path: /metrics

  # Design35 application metrics from OpenTelemetry Collector
  - job_name: 'design35-apps'
    static_configs:
      - targets: ['otel-collector:8889']
    scrape_interval: 15s
    metrics_path: /metrics



  # Elasticsearch metrics (if available)
  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['elasticsearch:9200']
    metrics_path: /_prometheus/metrics
    scrape_interval: 30s
    scrape_timeout: 10s

  # RabbitMQ metrics (if management plugin has prometheus support)
  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['rabbitmq:15692']
    metrics_path: /metrics
    scrape_interval: 30s
    scrape_timeout: 10s

  # MongoDB metrics (if exporter is available)
  - job_name: 'mongodb'
    static_configs:
      - targets: ['mongodb:27017']
    metrics_path: /metrics
    scrape_interval: 30s
    scrape_timeout: 10s
