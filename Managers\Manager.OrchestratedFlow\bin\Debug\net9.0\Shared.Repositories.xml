<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Shared.Repositories</name>
    </assembly>
    <members>
        <member name="T:Shared.Repositories.Base.BaseRepository`1">
            <summary>
            Abstract base repository class that provides common CRUD operations for all entities.
            Implements the repository pattern with MongoDB as the data store.
            </summary>
            <typeparam name="T">The entity type that inherits from BaseEntity.</typeparam>
        </member>
        <member name="F:Shared.Repositories.Base.BaseRepository`1._collection">
            <summary>
            The MongoDB collection for the entity type.
            </summary>
        </member>
        <member name="F:Shared.Repositories.Base.BaseRepository`1._logger">
            <summary>
            Logger instance for the repository.
            </summary>
        </member>
        <member name="F:Shared.Repositories.Base.BaseRepository`1._eventPublisher">
            <summary>
            Event publisher for domain events.
            </summary>
        </member>
        <member name="F:Shared.Repositories.Base.BaseRepository`1._metricsService">
            <summary>
            Manager metrics service for recording business metrics.
            </summary>
        </member>
        <member name="F:Shared.Repositories.Base.BaseRepository`1.ActivitySource">
            <summary>
            Activity source for distributed tracing.
            </summary>
        </member>
        <member name="M:Shared.Repositories.Base.BaseRepository`1.#ctor(MongoDB.Driver.IMongoDatabase,System.String,Microsoft.Extensions.Logging.ILogger{Shared.Repositories.Base.BaseRepository{`0}},Shared.Services.IEventPublisher,Shared.Services.IManagerMetricsService)">
            <summary>
            Initializes a new instance of the BaseRepository class.
            </summary>
            <param name="database">The MongoDB database instance.</param>
            <param name="collectionName">The name of the collection for this entity type.</param>
            <param name="logger">The logger instance.</param>
            <param name="eventPublisher">The event publisher for domain events.</param>
            <param name="metricsService">The manager metrics service for recording business metrics.</param>
        </member>
        <member name="M:Shared.Repositories.Base.BaseRepository`1.GetByIdAsync(System.Guid)">
            <summary>
            Gets an entity by its unique identifier.
            </summary>
            <param name="id">The unique identifier of the entity.</param>
            <returns>The entity if found, otherwise null.</returns>
        </member>
        <member name="M:Shared.Repositories.Base.BaseRepository`1.GetByCompositeKeyAsync(System.String)">
            <summary>
            Gets an entity by its composite key.
            </summary>
            <param name="compositeKey">The composite key of the entity.</param>
            <returns>The entity if found, otherwise null.</returns>
        </member>
        <member name="M:Shared.Repositories.Base.BaseRepository`1.GetAllAsync">
            <summary>
            Gets all entities of the specified type.
            </summary>
            <returns>A collection of all entities.</returns>
        </member>
        <member name="M:Shared.Repositories.Base.BaseRepository`1.GetPagedAsync(System.Int32,System.Int32)">
            <summary>
            Gets a paged collection of entities.
            </summary>
            <param name="page">The page number (1-based).</param>
            <param name="pageSize">The number of entities per page.</param>
            <returns>A collection of entities for the specified page.</returns>
        </member>
        <member name="M:Shared.Repositories.Base.BaseRepository`1.CreateAsync(`0)">
            <summary>
            Creates a new entity.
            </summary>
            <param name="entity">The entity to create.</param>
            <returns>The created entity with generated ID and timestamps.</returns>
        </member>
        <member name="M:Shared.Repositories.Base.BaseRepository`1.UpdateAsync(`0)">
            <summary>
            Updates an existing entity.
            </summary>
            <param name="entity">The entity to update.</param>
            <returns>The updated entity.</returns>
        </member>
        <member name="M:Shared.Repositories.Base.BaseRepository`1.DeleteAsync(System.Guid)">
            <summary>
            Deletes an entity by its unique identifier.
            </summary>
            <param name="id">The unique identifier of the entity to delete.</param>
            <returns>True if the entity was deleted, false if not found.</returns>
        </member>
        <member name="M:Shared.Repositories.Base.BaseRepository`1.ExistsAsync(System.String)">
            <summary>
            Checks if an entity exists with the specified composite key.
            </summary>
            <param name="compositeKey">The composite key to check.</param>
            <returns>True if an entity exists with the composite key, otherwise false.</returns>
        </member>
        <member name="M:Shared.Repositories.Base.BaseRepository`1.ExistsByIdAsync(System.Guid)">
            <summary>
            Checks if an entity exists with the specified unique identifier.
            </summary>
            <param name="id">The unique identifier to check.</param>
            <returns>True if an entity exists with the ID, otherwise false.</returns>
        </member>
        <member name="M:Shared.Repositories.Base.BaseRepository`1.CountAsync">
            <summary>
            Gets the total count of entities.
            </summary>
            <returns>The total number of entities.</returns>
        </member>
        <member name="M:Shared.Repositories.Base.BaseRepository`1.CreateCompositeKeyFilter(System.String)">
            <summary>
            Creates a filter definition for finding entities by composite key.
            Must be implemented by derived classes to define how composite keys are matched.
            </summary>
            <param name="compositeKey">The composite key to create a filter for.</param>
            <returns>A filter definition for the composite key.</returns>
        </member>
        <member name="M:Shared.Repositories.Base.BaseRepository`1.CreateIndexes">
            <summary>
            Creates database indexes for the entity collection.
            Should be implemented by derived classes to define appropriate indexes.
            </summary>
        </member>
        <member name="M:Shared.Repositories.Base.BaseRepository`1.PublishCreatedEventAsync(`0)">
            <summary>
            Publishes a domain event when an entity is created.
            Must be implemented by derived classes to publish appropriate events.
            </summary>
            <param name="entity">The created entity.</param>
        </member>
        <member name="M:Shared.Repositories.Base.BaseRepository`1.PublishUpdatedEventAsync(`0)">
            <summary>
            Publishes a domain event when an entity is updated.
            Must be implemented by derived classes to publish appropriate events.
            </summary>
            <param name="entity">The updated entity.</param>
        </member>
        <member name="M:Shared.Repositories.Base.BaseRepository`1.PublishDeletedEventAsync(System.Guid,System.String)">
            <summary>
            Publishes a domain event when an entity is deleted.
            Must be implemented by derived classes to publish appropriate events.
            </summary>
            <param name="id">The ID of the deleted entity.</param>
            <param name="deletedBy">The user who deleted the entity.</param>
        </member>
        <member name="T:Shared.Repositories.Interfaces.IBaseRepository`1">
            <summary>
            Base repository interface that defines common CRUD operations for all entities.
            </summary>
            <typeparam name="T">The entity type that inherits from BaseEntity.</typeparam>
        </member>
        <member name="M:Shared.Repositories.Interfaces.IBaseRepository`1.GetByIdAsync(System.Guid)">
            <summary>
            Gets an entity by its unique identifier.
            </summary>
            <param name="id">The unique identifier of the entity.</param>
            <returns>The entity if found, otherwise null.</returns>
        </member>
        <member name="M:Shared.Repositories.Interfaces.IBaseRepository`1.GetByCompositeKeyAsync(System.String)">
            <summary>
            Gets an entity by its composite key.
            </summary>
            <param name="compositeKey">The composite key of the entity.</param>
            <returns>The entity if found, otherwise null.</returns>
        </member>
        <member name="M:Shared.Repositories.Interfaces.IBaseRepository`1.GetAllAsync">
            <summary>
            Gets all entities of the specified type.
            </summary>
            <returns>A collection of all entities.</returns>
        </member>
        <member name="M:Shared.Repositories.Interfaces.IBaseRepository`1.GetPagedAsync(System.Int32,System.Int32)">
            <summary>
            Gets a paged collection of entities.
            </summary>
            <param name="page">The page number (1-based).</param>
            <param name="pageSize">The number of entities per page.</param>
            <returns>A collection of entities for the specified page.</returns>
        </member>
        <member name="M:Shared.Repositories.Interfaces.IBaseRepository`1.CreateAsync(`0)">
            <summary>
            Creates a new entity.
            </summary>
            <param name="entity">The entity to create.</param>
            <returns>The created entity with generated ID and timestamps.</returns>
        </member>
        <member name="M:Shared.Repositories.Interfaces.IBaseRepository`1.UpdateAsync(`0)">
            <summary>
            Updates an existing entity.
            </summary>
            <param name="entity">The entity to update.</param>
            <returns>The updated entity.</returns>
        </member>
        <member name="M:Shared.Repositories.Interfaces.IBaseRepository`1.DeleteAsync(System.Guid)">
            <summary>
            Deletes an entity by its unique identifier.
            </summary>
            <param name="id">The unique identifier of the entity to delete.</param>
            <returns>True if the entity was deleted, false if not found.</returns>
        </member>
        <member name="M:Shared.Repositories.Interfaces.IBaseRepository`1.ExistsAsync(System.String)">
            <summary>
            Checks if an entity exists with the specified composite key.
            </summary>
            <param name="compositeKey">The composite key to check.</param>
            <returns>True if an entity exists with the composite key, otherwise false.</returns>
        </member>
        <member name="M:Shared.Repositories.Interfaces.IBaseRepository`1.ExistsByIdAsync(System.Guid)">
            <summary>
            Checks if an entity exists with the specified unique identifier.
            </summary>
            <param name="id">The unique identifier to check.</param>
            <returns>True if an entity exists with the ID, otherwise false.</returns>
        </member>
        <member name="M:Shared.Repositories.Interfaces.IBaseRepository`1.CountAsync">
            <summary>
            Gets the total count of entities.
            </summary>
            <returns>The total number of entities.</returns>
        </member>
    </members>
</doc>
