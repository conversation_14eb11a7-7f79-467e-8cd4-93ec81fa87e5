﻿{"$schema":"http://json-schema.org/draft-07/schema#","type":"object","title":"ProcessedActivityData","description":"Data structure for processed activity information exchanged between orchestrator and processor","properties":{"processorId":{"type":"string","description":"ID of the processor that handled the activity","format":"uuid"},"orchestratedFlowEntityId":{"type":"string","description":"ID of the orchestrated flow entity","format":"uuid"},"entitiesProcessed":{"type":"integer","description":"Number of entities that were processed","minimum":0},"processingDetails":{"type":"object","description":"Detailed information about the processing operation","properties":{"processedAt":{"type":"string","description":"Timestamp when the processing occurred","format":"date-time"},"processingDuration":{"type":"string","description":"Duration of the processing operation","pattern":"^\\d+ms$"},"inputDataReceived":{"type":"boolean","description":"Whether input data was successfully received"},"inputMetadataReceived":{"type":"boolean","description":"Whether input metadata was successfully received"},"sampleData":{"type":"string","description":"Sample data or description of the processing operation"},"entityTypes":{"type":"array","description":"Array of entity type names that were processed","items":{"type":"string"}},"entities":{"type":"array","description":"Array of processed entity information","items":{"type":"object","properties":{"entityId":{"type":"string","description":"ID of the processed entity","format":"uuid"},"type":{"type":"string","description":"Type name of the entity"},"assignmentType":{"type":"string","description":"Assignment type of the entity"}},"required":["entityId","type","assignmentType"],"additionalProperties":false}}},"required":["processedAt","processingDuration","inputDataReceived","inputMetadataReceived","sampleData","entityTypes","entities"],"additionalProperties":false}},"required":["processorId","orchestratedFlowEntityId","entitiesProcessed","processingDetails"],"additionalProperties":false}