{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "MassTransit": "Information", "Hazelcast": "Information", "Manager.Orchestrator": "Information"}}, "RabbitMQ": {"Host": "${RABBITMQ_HOST:localhost}", "Port": "${RABBITMQ_PORT:5672}", "VirtualHost": "${RABBITMQ_VIRTUAL_HOST:/}", "Username": "${RABBITMQ_USERNAME}", "Password": "${RABBITMQ_PASSWORD}", "RetryLimit": 3, "RetryInterval": "00:00:30", "PrefetchCount": 16, "ConcurrencyLimit": 10}, "Hazelcast": {"ClusterName": "${HAZELCAST_CLUSTER_NAME:EntitiesManager}", "NetworkConfig": {"Addresses": ["${HAZELCAST_ADDRESS:127.0.0.1:5701}"]}, "ConnectionTimeout": "00:00:30"}, "OpenTelemetry": {"Endpoint": "${OTEL_EXPORTER_OTLP_ENDPOINT:http://localhost:4317}", "ServiceName": "Orchestrator<PERSON><PERSON><PERSON>", "ServiceVersion": "1.0.0"}, "ManagerUrls": {"Step": "${STEP_MANAGER_URL:http://localhost:5020}", "Assignment": "${ASSIGNMENT_MANAGER_URL:http://localhost:5010}", "Schema": "${SCHEMA_MANAGER_URL:http://localhost:5040}", "OrchestratedFlow": "${ORCHESTRATED_FLOW_MANAGER_URL:http://localhost:5050}"}, "OrchestrationCache": {"MapName": "orchestration-data", "DefaultTtlMinutes": 120, "MaxRetries": 5, "RetryDelayMs": 2000}, "HttpClient": {"TimeoutSeconds": 60, "MaxRetries": 5, "RetryDelayMs": 2000}, "HealthChecks": {"UI": {"EvaluationTimeInSeconds": 10, "MinimumSecondsBetweenFailureNotifications": 60}}, "Kestrel": {"Endpoints": {"Http": {"Url": "http://0.0.0.0:5000"}, "Https": {"Url": "https://0.0.0.0:5001"}}}}