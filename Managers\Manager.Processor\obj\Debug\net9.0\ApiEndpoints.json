[{"ContainingType": "Manager.Processor.Controllers.ProcessorController", "Method": "GetAll", "RelativePath": "api/Processor", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.ProcessorEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Processor.Controllers.ProcessorController", "Method": "Create", "RelativePath": "api/Processor", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "entity", "Type": "Shared.Entities.ProcessorEntity", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Entities.ProcessorEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Processor.Controllers.ProcessorController", "Method": "GetById", "RelativePath": "api/Processor/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Entities.ProcessorEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Processor.Controllers.ProcessorController", "Method": "Update", "RelativePath": "api/Processor/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "entity", "Type": "Shared.Entities.ProcessorEntity", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Entities.ProcessorEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Processor.Controllers.ProcessorController", "Method": "Delete", "RelativePath": "api/Processor/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Manager.Processor.Controllers.ProcessorController", "Method": "CheckProcessorExists", "RelativePath": "api/Processor/{processorId}/exists", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "processorId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Processor.Controllers.ProcessorController", "Method": "GetByCompositeKeyEmpty", "RelativePath": "api/Processor/composite", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Shared.Entities.ProcessorEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Processor.Controllers.ProcessorController", "Method": "GetByCompositeKey", "RelativePath": "api/Processor/composite/{version}/{name}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "version", "Type": "System.String", "IsRequired": true}, {"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Entities.ProcessorEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Processor.Controllers.ProcessorController", "Method": "GetByInputSchemaId", "RelativePath": "api/Processor/input-schema/{inputSchemaId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "inputSchemaId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.ProcessorEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Processor.Controllers.ProcessorController", "Method": "CheckInputSchemaReference", "RelativePath": "api/Processor/input-schema/{schemaId}/exists", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schemaId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Processor.Controllers.ProcessorController", "Method": "GetByName", "RelativePath": "api/Processor/name/{name}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.ProcessorEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Processor.Controllers.ProcessorController", "Method": "GetByOutputSchemaId", "RelativePath": "api/Processor/output-schema/{outputSchemaId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "outputSchemaId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.ProcessorEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Processor.Controllers.ProcessorController", "Method": "CheckOutputSchemaReference", "RelativePath": "api/Processor/output-schema/{schemaId}/exists", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schemaId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Processor.Controllers.ProcessorController", "Method": "GetPaged", "RelativePath": "api/Processor/paged", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Manager.Processor.Controllers.ProcessorController", "Method": "GetByVersion", "RelativePath": "api/Processor/version/{version}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "version", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.ProcessorEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]