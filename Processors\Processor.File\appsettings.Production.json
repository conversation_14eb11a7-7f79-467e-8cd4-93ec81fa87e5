{"Logging": {"LogLevel": {"Default": "Warning", "Microsoft.AspNetCore": "Warning", "MassTransit": "Warning", "Hazelcast": "Warning", "FileProcessorApplication": "Information"}}, "ProcessorConfiguration": {"Version": "1.1.1", "Name": "FileProcessor", "Description": "File processor application v2.0.0 for production file processing"}, "OpenTelemetry": {"Endpoint": "http://localhost:4317", "UseConsoleInDevelopment": false, "ServiceName": "FileProcessor", "ServiceVersion": "2.0.0"}, "Hazelcast": {"ClusterName": "EntitiesManager"}, "SchemaValidation": {"LogValidationWarnings": false, "LogValidationErrors": true}}