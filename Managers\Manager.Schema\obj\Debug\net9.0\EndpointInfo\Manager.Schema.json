{"openapi": "3.0.1", "info": {"title": "<PERSON><PERSON>", "version": "1.0"}, "paths": {"/api/Schema": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SchemaEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SchemaEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SchemaEntity"}}}}}}}, "post": {"tags": ["<PERSON><PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SchemaEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SchemaEntity"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SchemaEntity"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SchemaEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SchemaEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SchemaEntity"}}}}}}}, "/api/Schema/paged": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK"}}}}, "/api/Schema/{id}": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SchemaEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SchemaEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SchemaEntity"}}}}}}, "put": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SchemaEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SchemaEntity"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SchemaEntity"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SchemaEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SchemaEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SchemaEntity"}}}}}}, "delete": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Schema/composite/{version}/{name}": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SchemaEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SchemaEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SchemaEntity"}}}}}}}, "/api/Schema/definition/{definition}": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "definition", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SchemaEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SchemaEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SchemaEntity"}}}}}}}}, "/api/Schema/version/{version}": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SchemaEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SchemaEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SchemaEntity"}}}}}}}}, "/api/Schema/name/{name}": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SchemaEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SchemaEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SchemaEntity"}}}}}}}}, "/api/Schema/{id}/exists": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/Schema/composite": {"get": {"tags": ["<PERSON><PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SchemaEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SchemaEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SchemaEntity"}}}}}}}}, "components": {"schemas": {"SchemaEntity": {"required": ["definition", "name", "version"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "version": {"maxLength": 50, "minLength": 0, "pattern": "^\\d+\\.\\d+\\.\\d+$", "type": "string"}, "name": {"maxLength": 50, "minLength": 0, "type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "nullable": true}, "updatedBy": {"type": "string", "nullable": true}, "description": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}, "isNew": {"type": "boolean", "readOnly": true}, "definition": {"minLength": 1, "type": "string"}}, "additionalProperties": false}}}}