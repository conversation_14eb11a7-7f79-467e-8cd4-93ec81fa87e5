﻿{
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "Microsoft.AspNetCore": "Warning",
        "System": "Warning",
        "MassTransit": "Information"
      }
    },
    "WriteTo": [
      {
        "Name": "Console",
        "Args": {
          "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}",
          "restrictedToMinimumLevel": "Information"
        }
      },
      {
        "Name": "File",
        "Args": {
          "path": "/app/logs/entitiesmanager-.log",
          "rollingInterval": "Day",
          "retainedFileCountLimit": 30,
          "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {SourceContext}: {Message:lj}{NewLine}{Exception}"
        }
      }
    ],
    "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.AspNetCore.Hosting": "Information",
      "Microsoft.AspNetCore.Routing": "Warning",
      "MassTransit": "Information",
      "MongoDB": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "MongoDB": "${MONGODB_CONNECTION_STRING}"
  },
  "MongoDB": {
    "DatabaseName": "${MONGODB_DATABASE_NAME:ManagerDeliveryDB}",
    "MaxConnectionPoolSize": 100,
    "ConnectTimeout": "30s",
    "ServerSelectionTimeout": "30s",
    "SocketTimeout": "30s"
  },
  "RabbitMQ": {
    "Host": "${RABBITMQ_HOST:localhost}",
    "Port": "${RABBITMQ_PORT:5672}",
    "VirtualHost": "${RABBITMQ_VIRTUAL_HOST:/}",
    "Username": "${RABBITMQ_USERNAME}",
    "Password": "${RABBITMQ_PASSWORD}",
    "RetryLimit": 3,
    "RetryInterval": "00:00:30",
    "PrefetchCount": 16,
    "ConcurrencyLimit": 10
  },
  "OpenTelemetry": {
    "Endpoint": "${OTEL_EXPORTER_OTLP_ENDPOINT:http://localhost:4317}",
    "ServiceName": "DeliveryManager",
    "ServiceVersion": "1.0.0"
  },
  "HealthChecks": {
    "UI": {
      "EvaluationTimeInSeconds": 10,
      "MinimumSecondsBetweenFailureNotifications": 60
    }
  },
  "Kestrel": {
    "Endpoints": {
      "Http": {
        "Url": "http://0.0.0.0:5000"
      },
      "Https": {
        "Url": "https://0.0.0.0:5001"
      }
    }
  }
}
