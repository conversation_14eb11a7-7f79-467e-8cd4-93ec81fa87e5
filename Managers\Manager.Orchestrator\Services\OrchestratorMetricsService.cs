using System.Diagnostics.Metrics;
using Microsoft.Extensions.Options;
using Shared.Models;
using Shared.Extensions;
using Shared.Correlation;

namespace Manager.Orchestrator.Services;

/// <summary>
/// Service for recording orchestrator metrics for monitoring publishing to steps and consuming from steps.
/// Provides comprehensive metrics for orchestration operations following the processor metrics design pattern.
/// Uses IOrchestratorMetricsLabelService for consistent labeling across all metrics.
/// Enhanced to support comprehensive health cache entry metrics.
/// </summary>
public class OrchestratorMetricsService : IOrchestratorMetricsService, IDisposable
{
    private readonly ManagerConfiguration _config;
    private readonly ILogger<OrchestratorMetricsService> _logger;
    private readonly IOrchestratorMetricsLabelService _labelService;
    private readonly Meter _meter;

    // Step Publishing Metrics (Core for Anomaly Detection)
    private readonly Counter<long> _stepCommandsPublishedCounter;
    private readonly Counter<long> _stepCommandsSuccessfulCounter;
    private readonly Counter<long> _stepCommandsFailedCounter;
    private readonly Gauge<long> _activeStepExecutionsGauge;

    // Step Consumption Metrics (Core for Anomaly Detection)
    private readonly Counter<long> _activityEventsConsumedCounter;
    private readonly Counter<long> _activityEventsSuccessfulCounter;
    private readonly Counter<long> _activityEventsFailedCounter;
    private readonly Counter<long> _flowBranchTerminationsCounter;

    // Flow Lifecycle Metrics (Essential)
    private readonly Counter<long> _orchestrationFlowsStartedCounter;
    private readonly Counter<long> _orchestrationFlowsCompletedCounter;
    private readonly Counter<long> _orchestrationFlowsFailedCounter;

    // Cache Operations Metrics (Simplified)
    private readonly Counter<long> _cacheOperationsCounter;
    private readonly Gauge<long> _activeCacheEntriesGauge;

    // Health Metrics (Simplified)
    private readonly Gauge<int> _healthStatusGauge;
    private readonly Counter<long> _healthCheckCounter;

    // NEW: Flow Anomaly Detection Metric
    private readonly Gauge<long> _stepFlowAnomalyGauge;

    // NEW: Comprehensive Health Cache Entry Metrics (following processor pattern)
    private readonly Gauge<int> _orchestratorStatusGauge;
    private readonly Gauge<double> _orchestratorUptimeGauge;
    private readonly Gauge<double> _orchestratorCpuUsageGauge;
    private readonly Gauge<long> _orchestratorMemoryUsageGauge;


    // NEW: Orchestration-specific Metrics
    private readonly Gauge<int> _activeFlowsGauge;
    private readonly Gauge<int> _activeStepExecutionsComprehensiveGauge;
    private readonly Gauge<double> _averageFlowExecutionTimeGauge;
    private readonly Gauge<double> _throughputFlowsPerMinuteGauge;



    public OrchestratorMetricsService(
        IOptions<ManagerConfiguration> config,
        ILogger<OrchestratorMetricsService> logger,
        IOrchestratorMetricsLabelService labelService)
    {
        _config = config.Value;
        _logger = logger;
        _labelService = labelService;

        // Use the recommended unique meter name pattern: {Version}_{Name}
        var meterName = $"{_config.Version}_{_config.Name}";
        _meter = new Meter($"{meterName}.Orchestrator");

        // Initialize step publishing metrics (Core for Anomaly Detection)
        _stepCommandsPublishedCounter = _meter.CreateCounter<long>(
            "orchestrator_step_commands_published_total",
            "Total number of step commands published by the orchestrator");

        _stepCommandsSuccessfulCounter = _meter.CreateCounter<long>(
            "orchestrator_step_commands_successful_total",
            "Total number of step commands successfully published");

        _stepCommandsFailedCounter = _meter.CreateCounter<long>(
            "orchestrator_step_commands_failed_total",
            "Total number of step commands that failed to publish");

        _activeStepExecutionsGauge = _meter.CreateGauge<long>(
            "orchestrator_active_step_executions_total",
            "Current number of active step executions");

        // Initialize step consumption metrics (Core for Anomaly Detection)
        _activityEventsConsumedCounter = _meter.CreateCounter<long>(
            "orchestrator_activity_events_consumed_total",
            "Total number of activity events consumed by the orchestrator");

        _activityEventsSuccessfulCounter = _meter.CreateCounter<long>(
            "orchestrator_activity_events_successful_total",
            "Total number of activity events successfully processed");

        _activityEventsFailedCounter = _meter.CreateCounter<long>(
            "orchestrator_activity_events_failed_total",
            "Total number of activity events that failed processing");

        _flowBranchTerminationsCounter = _meter.CreateCounter<long>(
            "orchestrator_flow_branch_terminations_total",
            "Total number of flow branch terminations detected");

        // Initialize flow lifecycle metrics (Essential)
        _orchestrationFlowsStartedCounter = _meter.CreateCounter<long>(
            "orchestrator_flows_started_total",
            "Total number of orchestration flows started");

        _orchestrationFlowsCompletedCounter = _meter.CreateCounter<long>(
            "orchestrator_flows_completed_total",
            "Total number of orchestration flows completed");

        _orchestrationFlowsFailedCounter = _meter.CreateCounter<long>(
            "orchestrator_flows_failed_total",
            "Total number of orchestration flows that failed");

        // Initialize cache operations metrics (Simplified)
        _cacheOperationsCounter = _meter.CreateCounter<long>(
            "orchestrator_cache_operations_total",
            "Total number of cache operations performed");

        _activeCacheEntriesGauge = _meter.CreateGauge<long>(
            "orchestrator_active_cache_entries_total",
            "Current number of active cache entries");

        // Initialize health metrics (Simplified)
        _healthStatusGauge = _meter.CreateGauge<int>(
            "orchestrator_health_status",
            "Current health status of the orchestrator (0=Healthy, 1=Degraded, 2=Unhealthy)");

        _healthCheckCounter = _meter.CreateCounter<long>(
            "orchestrator_health_checks_total",
            "Total number of health checks performed");

        // Initialize flow anomaly detection metric (NEW)
        _stepFlowAnomalyGauge = _meter.CreateGauge<long>(
            "orchestrator_step_flow_anomaly_difference",
            "Absolute difference between published and consumed amounts per step (anomaly indicator)");

        // Initialize comprehensive health cache entry metrics (NEW - following processor pattern)
        _orchestratorStatusGauge = _meter.CreateGauge<int>(
            "orchestrator_health_status_comprehensive",
            "Current health status of the orchestrator (0=Healthy, 1=Degraded, 2=Unhealthy)");

        _orchestratorUptimeGauge = _meter.CreateGauge<double>(
            "orchestrator_uptime_seconds",
            "How long the orchestrator has been running in seconds");

        _orchestratorCpuUsageGauge = _meter.CreateGauge<double>(
            "orchestrator_cpu_usage_percent",
            "Current CPU usage percentage of the orchestrator");

        _orchestratorMemoryUsageGauge = _meter.CreateGauge<long>(
            "orchestrator_memory_usage_bytes",
            "Current memory usage in bytes of the orchestrator");



        // Initialize orchestration-specific metrics (NEW)
        _activeFlowsGauge = _meter.CreateGauge<int>(
            "orchestrator_active_flows_total",
            "Current number of active orchestration flows");

        _activeStepExecutionsComprehensiveGauge = _meter.CreateGauge<int>(
            "orchestrator_active_step_executions_comprehensive_total",
            "Current number of active step executions (comprehensive)");

        _averageFlowExecutionTimeGauge = _meter.CreateGauge<double>(
            "orchestrator_average_flow_execution_time_seconds",
            "Average flow execution time in seconds");

        _throughputFlowsPerMinuteGauge = _meter.CreateGauge<double>(
            "orchestrator_throughput_flows_per_minute",
            "Current orchestration throughput in flows per minute");

        _logger.LogInformationWithCorrelation(
            "OrchestratorMetricsService initialized with comprehensive metrics. Meter name: {MeterName}, Composite Key: {CompositeKey}",
            $"{meterName}.Orchestrator", _labelService.OrchestratorCompositeKey);
    }

    // Step Publishing Methods

    public void RecordStepCommandPublished(bool success, string stepVersionName)
    {
        var tags = _labelService.GetStepPublishLabels(stepVersionName, "publish_step_command", success ? "success" : "failed");

        _stepCommandsPublishedCounter.Add(1, tags);

        if (success)
            _stepCommandsSuccessfulCounter.Add(1, tags);
        else
            _stepCommandsFailedCounter.Add(1, tags);

        _logger.LogDebugWithCorrelation(
            "Recorded step command published metrics: StepVersionName={StepVersionName}, Success={Success}",
            stepVersionName, success);
    }



    public void UpdateActiveStepExecutions(long count)
    {
        var tags = _labelService.GetSystemLabels();
        _activeStepExecutionsGauge.Record(count, tags);

        _logger.LogDebugWithCorrelation(
            "Updated active step executions: Count={Count}",
            count);
    }

    // Step Consumption Methods

    public void RecordActivityEventProcessed(bool success, string stepVersionName, string orchestratedFlowVersionName, bool isFlowTermination = false)
    {
        var tags = _labelService.GetStepConsumptionLabels(stepVersionName, orchestratedFlowVersionName, "process_activity_event", success ? "success" : "failed");

        _activityEventsConsumedCounter.Add(1, tags);

        if (success)
            _activityEventsSuccessfulCounter.Add(1, tags);
        else
            _activityEventsFailedCounter.Add(1, tags);

        if (isFlowTermination)
            _flowBranchTerminationsCounter.Add(1, tags);

        _logger.LogDebugWithCorrelation(
            "Recorded activity event processed metrics: StepVersionName={StepVersionName}, OrchestratedFlowVersionName={OrchestratedFlowVersionName}, Success={Success}, IsFlowTermination={IsFlowTermination}",
            stepVersionName, orchestratedFlowVersionName, success, isFlowTermination);
    }

    // Flow Lifecycle Methods

    public void RecordOrchestrationFlow(bool success, string orchestratedFlowVersionName, string operation)
    {
        var tags = _labelService.GetFlowLabels(orchestratedFlowVersionName, operation, success ? "success" : "failed");

        // Record based on operation type
        switch (operation.ToLowerInvariant())
        {
            case "start":
                _orchestrationFlowsStartedCounter.Add(1, tags);
                break;
            case "complete":
                _orchestrationFlowsCompletedCounter.Add(1, tags);
                break;
            case "fail":
                _orchestrationFlowsFailedCounter.Add(1, tags);
                break;
        }

        _logger.LogDebugWithCorrelation(
            "Recorded orchestration flow metrics: OrchestratedFlowVersionName={OrchestratedFlowVersionName}, Operation={Operation}, Success={Success}",
            orchestratedFlowVersionName, operation, success);
    }

    // Cache Operations Methods

    public void RecordCacheOperation(bool success, string operationType)
    {
        var tags = _labelService.GetCacheLabels(operationType, success ? "success" : "failed");

        _cacheOperationsCounter.Add(1, tags);

        _logger.LogDebugWithCorrelation(
            "Recorded cache operation metrics: OperationType={OperationType}, Success={Success}",
            operationType, success);
    }

    public void UpdateActiveCacheEntries(long count)
    {
        var tags = _labelService.GetSystemLabels();
        _activeCacheEntriesGauge.Record(count, tags);

        _logger.LogDebugWithCorrelation(
            "Updated active cache entries: Count={Count}",
            count);
    }

    // Health Methods

    public void RecordHealthStatus(int status)
    {
        var tags = _labelService.GetSystemLabels();
        _healthStatusGauge.Record(status, tags);

        _logger.LogDebugWithCorrelation(
            "Recorded health status: Status={Status}",
            status);
    }

    public void RecordHealthCheck(string healthCheckName, string healthCheckStatus)
    {
        var tags = _labelService.GetHealthLabels(healthCheckName, healthCheckStatus);

        _healthCheckCounter.Add(1, tags);

        _logger.LogDebugWithCorrelation(
            "Recorded health check metrics: HealthCheckName={HealthCheckName}, Status={Status}",
            healthCheckName, healthCheckStatus);
    }

    // NEW: Flow Anomaly Detection Method
    public void RecordStepFlowAnomaly(string stepVersionName, long publishedCount, long consumedCount)
    {
        var difference = Math.Abs(publishedCount - consumedCount);
        var anomalyStatus = difference > 0 ? "anomaly_detected" : "healthy";
        var tags = _labelService.GetStepPublishLabels(stepVersionName, "flow_anomaly", anomalyStatus);

        _stepFlowAnomalyGauge.Record(difference, tags);

        if (difference > 0)
        {
            _logger.LogWarningWithCorrelation(
                "🚨 Flow anomaly detected for step {StepVersionName}: Published={Published}, Consumed={Consumed}, Difference={Difference}",
                stepVersionName, publishedCount, consumedCount, difference);
        }
        else
        {
            _logger.LogDebugWithCorrelation(
                "Flow balance healthy for step {StepVersionName}: Published={Published}, Consumed={Consumed}",
                stepVersionName, publishedCount, consumedCount);
        }
    }

    // NEW: Comprehensive Health Cache Entry Methods (following processor pattern)

    public void RecordHealthCacheEntryMetrics(OrchestratorHealthCacheEntry healthEntry)
    {
        try
        {
            var orchestratorName = _config.Name;
            var orchestratorVersion = _config.Version;

            _logger.LogDebugWithCorrelation(
                "🔥 DEBUG: RecordHealthCacheEntryMetrics called for OrchestratorId: {OrchestratorId}, Status: {Status}, OrchestratorName: {OrchestratorName}, Version: {OrchestratorVersion}",
                healthEntry.OrchestratorId, healthEntry.Status, orchestratorName, orchestratorVersion);

            // Record all metrics from the health cache entry
            RecordOrchestratorStatus(healthEntry.OrchestratorId, healthEntry.Status, orchestratorName, orchestratorVersion);
            RecordOrchestratorUptime(healthEntry.OrchestratorId, healthEntry.Uptime, orchestratorName, orchestratorVersion);
            RecordPerformanceMetrics(healthEntry.OrchestratorId, healthEntry.PerformanceMetrics, orchestratorName, orchestratorVersion);
            RecordOrchestratorMetadata(healthEntry.OrchestratorId, healthEntry.Metadata, orchestratorName, orchestratorVersion);
            RecordOrchestrationMetrics(healthEntry.OrchestratorId, healthEntry.OrchestrationMetrics, orchestratorName, orchestratorVersion);

            _logger.LogDebugWithCorrelation(
                "🔥 DEBUG: Successfully recorded ALL health cache entry metrics for orchestrator {OrchestratorId} ({OrchestratorName} v{OrchestratorVersion})",
                healthEntry.OrchestratorId, orchestratorName, orchestratorVersion);
        }
        catch (Exception ex)
        {
            _logger.LogWarningWithCorrelation(ex, "Failed to record health cache entry metrics");
        }
    }

    public void RecordOrchestratorStatus(Guid orchestratorId, Shared.Models.HealthStatus status, string orchestratorName, string orchestratorVersion)
    {
        var tags = _labelService.GetSystemLabels();
        var statusValue = (int)status;

        _orchestratorStatusGauge.Record(statusValue, tags);

        _logger.LogDebugWithCorrelation(
            "Recorded orchestrator status: OrchestratorId={OrchestratorId}, Status={Status}",
            orchestratorId, status);
    }

    public void RecordOrchestratorUptime(Guid orchestratorId, TimeSpan uptime, string orchestratorName, string orchestratorVersion)
    {
        var tags = _labelService.GetSystemLabels();
        _orchestratorUptimeGauge.Record(uptime.TotalSeconds, tags);

        _logger.LogDebugWithCorrelation(
            "Recorded orchestrator uptime: OrchestratorId={OrchestratorId}, Uptime={Uptime}",
            orchestratorId, uptime);
    }

    public void RecordPerformanceMetrics(Guid orchestratorId, OrchestratorPerformanceMetrics performanceMetrics, string orchestratorName, string orchestratorVersion)
    {
        var tags = _labelService.GetSystemLabels();

        // Record current resource usage as gauges
        _orchestratorCpuUsageGauge.Record(performanceMetrics.CpuUsagePercent, tags);
        _orchestratorMemoryUsageGauge.Record(performanceMetrics.MemoryUsageBytes, tags);

        _logger.LogDebugWithCorrelation(
            "🔥 DEBUG: Recorded performance metrics - OrchestratorId: {OrchestratorId}, CPU: {CpuUsage}%, Memory: {MemoryUsage} bytes",
            orchestratorId, performanceMetrics.CpuUsagePercent, performanceMetrics.MemoryUsageBytes);
    }

    public void RecordOrchestratorMetadata(Guid orchestratorId, OrchestratorMetadata metadata, string orchestratorName, string orchestratorVersion)
    {
        // Process ID and start metrics removed - not providing valuable monitoring information
        _logger.LogDebugWithCorrelation(
            "Orchestrator metadata processed (process ID and start metrics removed): OrchestratorId={OrchestratorId}, HostName={HostName}",
            orchestratorId, metadata.HostName);
    }

    public void RecordOrchestrationMetrics(Guid orchestratorId, OrchestrationMetrics orchestrationMetrics, string orchestratorName, string orchestratorVersion)
    {
        var tags = _labelService.GetSystemLabels();

        _activeFlowsGauge.Record(orchestrationMetrics.ActiveFlows, tags);
        _activeStepExecutionsComprehensiveGauge.Record(orchestrationMetrics.ActiveStepExecutions, tags);
        _averageFlowExecutionTimeGauge.Record(orchestrationMetrics.AverageFlowExecutionTime, tags);
        _throughputFlowsPerMinuteGauge.Record(orchestrationMetrics.ThroughputFlowsPerMinute, tags);

        _logger.LogDebugWithCorrelation(
            "🔥 DEBUG: Recorded orchestration metrics - OrchestratorId: {OrchestratorId}, ActiveFlows: {ActiveFlows}, ActiveSteps: {ActiveSteps}",
            orchestratorId, orchestrationMetrics.ActiveFlows, orchestrationMetrics.ActiveStepExecutions);
    }

    public void Dispose()
    {
        _meter?.Dispose();
        _logger.LogInformationWithCorrelation("OrchestratorMetricsService disposed");
    }
}
