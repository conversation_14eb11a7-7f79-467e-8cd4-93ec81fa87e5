[{"ContainingType": "Manager.Orchestrator.Controllers.OrchestrationController", "Method": "GetProcessorHealth", "RelativePath": "api/Orchestration/processor-health/{processorId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "processorId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Models.ProcessorHealthResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Manager.Orchestrator.Controllers.OrchestrationController", "Method": "GetProcessorsHealthByOrchestratedFlow", "RelativePath": "api/Orchestration/processors-health/{orchestratedFlowId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orchestratedFlowId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Models.ProcessorsHealthResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Manager.Orchestrator.Controllers.OrchestrationController", "Method": "StartScheduler", "RelativePath": "api/Orchestration/scheduler/start/{orchestratedFlowId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "orchestratedFlowId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "Manager.Orchestrator.Models.StartSchedulerRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 409}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Manager.Orchestrator.Controllers.OrchestrationController", "Method": "StopScheduler", "RelativePath": "api/Orchestration/scheduler/stop/{orchestratedFlowId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "orchestratedFlowId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Manager.Orchestrator.Controllers.OrchestrationController", "Method": "Start", "RelativePath": "api/Orchestration/start/{orchestratedFlowId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "orchestratedFlowId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Manager.Orchestrator.Controllers.OrchestrationController", "Method": "GetStatus", "RelativePath": "api/Orchestration/status/{orchestratedFlowId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "orchestratedFlowId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Manager.Orchestrator.Services.OrchestrationStatusModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "Manager.Orchestrator.Controllers.OrchestrationController", "Method": "Stop", "RelativePath": "api/Orchestration/stop/{orchestratedFlowId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "orchestratedFlowId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}]