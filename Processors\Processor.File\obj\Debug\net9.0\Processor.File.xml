<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Processor.File</name>
    </assembly>
    <members>
        <member name="T:Processor.File.FileProcessorApplication">
            <summary>
            Sample concrete implementation of BaseProcessorApplication
            Demonstrates how to create a specific processor service
            The base class now provides a complete default implementation that can be overridden if needed
            </summary>
        </member>
        <member name="M:Processor.File.FileProcessorApplication.ConfigureLogging(Microsoft.Extensions.Logging.ILoggingBuilder)">
            <summary>
            Override to add console logging for debugging
            </summary>
        </member>
        <member name="M:Processor.File.FileProcessorApplication.ConfigureServices(Microsoft.Extensions.DependencyInjection.IServiceCollection,Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            Override to add FileProcessor-specific services
            </summary>
        </member>
        <member name="M:Processor.File.FileProcessorApplication.ProcessActivityDataAsync(System.Guid,System.Guid,System.Guid,System.Guid,System.Collections.Generic.List{Shared.Models.AssignmentModel},System.String,System.Guid,System.Threading.CancellationToken)">
            <summary>
            Concrete implementation of the activity processing logic
            This is where the specific processor business logic is implemented
            Handles input parsing and validation internally
            </summary>
        </member>
        <member name="T:Processor.File.Services.FileProcessorMetricsService">
            <summary>
            Concrete metrics service for FileProcessor-specific metrics.
            Extends the base processor metrics with file processing specific measurements.
            Uses consistent labeling from appsettings configuration.
            </summary>
        </member>
        <member name="M:Processor.File.Services.FileProcessorMetricsService.RecordFileProcessed(System.Boolean,System.TimeSpan,System.Int64,System.Int32,System.String)">
            <summary>
            Records file processing completion metrics.
            </summary>
        </member>
        <member name="M:Processor.File.Services.FileProcessorMetricsService.RecordEntityExtraction(System.Int32,System.TimeSpan,System.String)">
            <summary>
            Records entity extraction metrics.
            </summary>
        </member>
        <member name="M:Processor.File.Services.FileProcessorMetricsService.RecordFileValidationError(System.String,System.String)">
            <summary>
            Records file validation error.
            </summary>
        </member>
        <member name="M:Processor.File.Services.FileProcessorMetricsService.RecordEntityValidationError(System.String,System.String)">
            <summary>
            Records entity validation error.
            </summary>
        </member>
        <member name="M:Processor.File.Services.FileProcessorMetricsService.UpdateFileQueueSize(System.Int64)">
            <summary>
            Updates the current file queue size.
            </summary>
        </member>
        <member name="M:Processor.File.Services.FileProcessorMetricsService.UpdateAverageFileSize(System.Double)">
            <summary>
            Updates the average file size metric.
            </summary>
        </member>
        <member name="M:Processor.File.Services.FileProcessorMetricsService.GetCurrentCorrelationId">
            <summary>
            Gets the current correlation ID from the activity context
            </summary>
        </member>
        <member name="T:Processor.File.ProcessorImplementationHash">
            <summary>
            Auto-generated class containing SHA-256 hash of processor implementation files.
            Used for runtime integrity validation to ensure version consistency.
            </summary>
        </member>
        <member name="P:Processor.File.ProcessorImplementationHash.Hash">
            <summary>
            SHA-256 hash of the processor implementation file: FileProcessorApplication.cs
            </summary>
        </member>
        <member name="F:Processor.File.ProcessorImplementationHash.Version">
            <summary>
            Processor version from assembly information.
            </summary>
        </member>
        <member name="F:Processor.File.ProcessorImplementationHash.Name">
            <summary>
            Processor name from assembly information.
            </summary>
        </member>
        <member name="F:Processor.File.ProcessorImplementationHash.GeneratedAt">
            <summary>
            Timestamp when hash was generated.
            </summary>
        </member>
        <member name="F:Processor.File.ProcessorImplementationHash.SourceFile">
            <summary>
            Source file that was hashed.
            </summary>
        </member>
    </members>
</doc>
