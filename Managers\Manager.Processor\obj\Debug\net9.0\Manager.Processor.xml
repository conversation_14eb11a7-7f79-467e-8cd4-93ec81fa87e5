<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Manager.Processor</name>
    </assembly>
    <members>
        <member name="M:Manager.Processor.Controllers.ProcessorController.CheckInputSchemaReference(System.Guid)">
            <summary>
            Check if a schema is referenced as input schema by any processor entities
            </summary>
            <param name="schemaId">The schema ID to check</param>
            <returns>True if schema is referenced as input schema, false otherwise</returns>
        </member>
        <member name="M:Manager.Processor.Controllers.ProcessorController.CheckOutputSchemaReference(System.Guid)">
            <summary>
            Check if a schema is referenced as output schema by any processor entities
            </summary>
            <param name="schemaId">The schema ID to check</param>
            <returns>True if schema is referenced as output schema, false otherwise</returns>
        </member>
        <member name="M:Manager.Processor.Controllers.ProcessorController.CheckProcessorExists(System.String)">
            <summary>
            Check if a processor exists (used for referential integrity validation)
            </summary>
            <param name="processorId">The processor ID to check</param>
            <returns>True if processor exists, false otherwise</returns>
        </member>
        <member name="M:Manager.Processor.Repositories.IProcessorEntityRepository.HasInputSchemaReferences(System.Guid)">
            <summary>
            Check if any processor entities reference the specified schema ID as input schema
            </summary>
            <param name="schemaId">The schema ID to check for references</param>
            <returns>True if any processor entities reference the schema as input schema, false otherwise</returns>
        </member>
        <member name="M:Manager.Processor.Repositories.IProcessorEntityRepository.HasOutputSchemaReferences(System.Guid)">
            <summary>
            Check if any processor entities reference the specified schema ID as output schema
            </summary>
            <param name="schemaId">The schema ID to check for references</param>
            <returns>True if any processor entities reference the schema as output schema, false otherwise</returns>
        </member>
        <member name="T:Manager.Processor.Services.EntityReferenceValidator">
            <summary>
            Service for validating processor entity references with fail-safe behavior
            </summary>
        </member>
        <member name="T:Manager.Processor.Services.IEntityReferenceValidator">
            <summary>
            Interface for validating processor entity references across all entity managers
            </summary>
        </member>
        <member name="M:Manager.Processor.Services.IEntityReferenceValidator.HasStepReferences(System.Guid)">
            <summary>
            Check if a processor has any references in step entities
            </summary>
            <param name="processorId">The processor ID to check</param>
            <returns>True if processor has references, false otherwise</returns>
        </member>
        <member name="M:Manager.Processor.Services.IEntityReferenceValidator.ValidateProcessorCanBeDeleted(System.Guid)">
            <summary>
            Validate that a processor entity can be safely deleted
            </summary>
            <param name="processorId">The processor ID to validate</param>
            <exception cref="T:System.InvalidOperationException">Thrown if processor cannot be deleted due to references</exception>
        </member>
        <member name="M:Manager.Processor.Services.IEntityReferenceValidator.ValidateProcessorCanBeUpdated(System.Guid,Shared.Entities.ProcessorEntity,Shared.Entities.ProcessorEntity)">
            <summary>
            Validate that a processor entity can be safely updated with critical property changes
            </summary>
            <param name="processorId">The processor ID to validate</param>
            <param name="existingEntity">The existing processor entity</param>
            <param name="updatedEntity">The updated processor entity</param>
            <exception cref="T:System.InvalidOperationException">Thrown if processor cannot be updated due to references</exception>
        </member>
        <member name="T:Manager.Processor.Services.IManagerHttpClient">
            <summary>
            Interface for HTTP client communication with other entity managers
            </summary>
        </member>
        <member name="M:Manager.Processor.Services.IManagerHttpClient.CheckProcessorReferencesInSteps(System.Guid)">
            <summary>
            Check if a processor is referenced by any step entities
            </summary>
            <param name="processorId">The processor ID to check</param>
            <returns>True if processor has references in step entities, false otherwise</returns>
        </member>
        <member name="M:Manager.Processor.Services.IManagerHttpClient.CheckSchemaExists(System.Guid)">
            <summary>
            Check if a schema exists in the Schema Manager
            </summary>
            <param name="schemaId">The schema ID to check</param>
            <returns>True if schema exists, false otherwise</returns>
        </member>
        <member name="T:Manager.Processor.Services.ISchemaValidationService">
            <summary>
            Interface for validating schema references during processor entity operations
            </summary>
        </member>
        <member name="M:Manager.Processor.Services.ISchemaValidationService.ValidateSchemasExist(System.Guid,System.Guid)">
            <summary>
            Validates that both input and output schemas exist before creating or updating a processor entity
            </summary>
            <param name="inputSchemaId">The input schema ID to validate</param>
            <param name="outputSchemaId">The output schema ID to validate</param>
            <returns>Task that completes successfully if both schemas exist</returns>
            <exception cref="T:System.InvalidOperationException">Thrown if either schema doesn't exist or validation fails</exception>
        </member>
        <member name="T:Manager.Processor.Services.ManagerHttpClient">
            <summary>
            HTTP client for communication with other entity managers with resilience patterns
            </summary>
        </member>
        <member name="T:Manager.Processor.Services.SchemaValidationService">
            <summary>
            Service for validating dual schema references during processor entity operations
            Implements strong consistency model with fail-safe approach
            </summary>
        </member>
    </members>
</doc>
