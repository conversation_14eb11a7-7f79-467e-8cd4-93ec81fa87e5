[{"ContainingType": "Manager.Delivery.Controllers.DeliveryController", "Method": "GetAll", "RelativePath": "api/Delivery", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.DeliveryEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Delivery.Controllers.DeliveryController", "Method": "Create", "RelativePath": "api/Delivery", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "entity", "Type": "Shared.Entities.DeliveryEntity", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Entities.DeliveryEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Delivery.Controllers.DeliveryController", "Method": "GetById", "RelativePath": "api/Delivery/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Entities.DeliveryEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Delivery.Controllers.DeliveryController", "Method": "Update", "RelativePath": "api/Delivery/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "entity", "Type": "Shared.Entities.DeliveryEntity", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Entities.DeliveryEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Delivery.Controllers.DeliveryController", "Method": "Delete", "RelativePath": "api/Delivery/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Manager.Delivery.Controllers.DeliveryController", "Method": "GetByCompositeKeyEmpty", "RelativePath": "api/Delivery/composite", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Shared.Entities.DeliveryEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Delivery.Controllers.DeliveryController", "Method": "GetByCompositeKey", "RelativePath": "api/Delivery/composite/{version}/{name}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "version", "Type": "System.String", "IsRequired": true}, {"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Entities.DeliveryEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Delivery.Controllers.DeliveryController", "Method": "GetByName", "RelativePath": "api/Delivery/name/{name}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.DeliveryEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Delivery.Controllers.DeliveryController", "Method": "GetPaged", "RelativePath": "api/Delivery/paged", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Manager.Delivery.Controllers.DeliveryController", "Method": "GetByPayload", "RelativePath": "api/Delivery/payload/{payload}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "payload", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.DeliveryEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Delivery.Controllers.DeliveryController", "Method": "CheckSchemaReference", "RelativePath": "api/Delivery/schema/{schemaId}/exists", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schemaId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Delivery.Controllers.DeliveryController", "Method": "GetBySchemaId", "RelativePath": "api/Delivery/schemaId/{schemaId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "schemaId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.DeliveryEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Delivery.Controllers.DeliveryController", "Method": "GetByVersion", "RelativePath": "api/Delivery/version/{version}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "version", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.DeliveryEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]