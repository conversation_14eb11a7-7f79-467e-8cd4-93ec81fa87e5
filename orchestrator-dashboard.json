{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"id": 1, "title": "Orchestrator Health & Status", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "collapsed": false}, {"id": 2, "title": "Orchestrator Health Status (Comprehensive)", "description": "Current comprehensive health status of the orchestrator. Values: 0=Healthy (Green), 1=Degraded (Yellow), 2=Unhealthy (Red).", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "orchestrator_health_status_comprehensive_Current_health_status_of_the_orchestrator_0_Healthy_1_Degraded_2_Unhealthy{orchestrator_composite_key=~\"$orchestrator_composite_key\"}", "instant": false, "legendFormat": "{{orchestrator_composite_key}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1}, {"color": "red", "value": 2}]}, "mappings": [{"options": {"0": {"text": "Healthy"}, "1": {"text": "Degraded"}, "2": {"text": "Unhealthy"}}, "type": "value"}]}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 1}}, {"id": 3, "title": "Activity Events Processed", "description": "Total number of activity events processed by the orchestrator.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(orchestrator_activity_events_consumed_Total_number_of_activity_events_consumed_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"})", "instant": false, "legendFormat": "Total Events", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 1}}, {"id": 4, "title": "Active Cache Entries", "description": "Current number of active cache entries maintained by the orchestrator.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "orchestrator_active_cache_entries_total_Current_number_of_active_cache_entries{orchestrator_composite_key=~\"$orchestrator_composite_key\"}", "instant": false, "legendFormat": "{{orchestrator_composite_key}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 1}}, {"id": 5, "title": "Step Commands Published", "description": "Total number of step commands published by the orchestrator.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(orchestrator_step_commands_published_Total_number_of_step_commands_published_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"})", "instant": false, "legendFormat": "Total Commands", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "blue", "value": 0}]}}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 1}}, {"id": 29, "title": "Active Orchestration Flows", "description": "Current number of active orchestration flows.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "orchestrator_active_flows_total_Current_number_of_active_orchestration_flows{orchestrator_composite_key=~\"$orchestrator_composite_key\"}", "instant": false, "legendFormat": "Active Flows", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "blue", "value": 0}]}}}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 9}}, {"id": 30, "title": "Orchestrator Uptime", "description": "How long the orchestrator has been running.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "orchestrator_uptime_seconds_How_long_the_orchestrator_has_been_running_in_seconds{orchestrator_composite_key=~\"$orchestrator_composite_key\"}", "instant": false, "legendFormat": "{{orchestrator_composite_key}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "s", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}]}}}, "gridPos": {"h": 4, "w": 6, "x": 6, "y": 9}}, {"id": 31, "title": "CPU Usage", "description": "Current CPU usage percentage of the orchestrator.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "orchestrator_cpu_usage_percent_Current_CPU_usage_percentage_of_the_orchestrator{orchestrator_composite_key=~\"$orchestrator_composite_key\"}", "instant": false, "legendFormat": "{{orchestrator_composite_key}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 70}, {"color": "red", "value": 90}]}, "min": 0, "max": 100}}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 9}}, {"id": 32, "title": "Memory Usage", "description": "Current memory usage in bytes of the orchestrator.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "orchestrator_memory_usage_bytes_Current_memory_usage_in_bytes_of_the_orchestrator{orchestrator_composite_key=~\"$orchestrator_composite_key\"}", "instant": false, "legendFormat": "{{orchestrator_composite_key}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "bytes", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 500000000}, {"color": "red", "value": 1000000000}]}}}, "gridPos": {"h": 4, "w": 6, "x": 18, "y": 9}}, {"id": 34, "title": "Orchestration Throughput", "description": "Current orchestration throughput in flows per minute.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "orchestrator_throughput_flows_per_minute_Current_orchestration_throughput_in_flows_per_minute{orchestrator_composite_key=~\"$orchestrator_composite_key\"}", "instant": false, "legendFormat": "{{orchestrator_composite_key}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "blue", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 4, "w": 24, "x": 0, "y": 13}}, {"id": 6, "title": "Step Command Publishing Metrics", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 17}, "collapsed": false}, {"id": 7, "title": "Step Commands Published (Total)", "description": "Total number of step commands published by the orchestrator to processors.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(rate(orchestrator_step_commands_published_Total_number_of_step_commands_published_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"}[5m]))", "instant": false, "legendFormat": "Published Rate", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 18}}, {"id": 8, "title": "Step Commands Successful", "description": "Total number of step commands successfully published.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(orchestrator_step_commands_successful_Total_number_of_step_commands_successfully_published_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"})", "instant": false, "legendFormat": "Successful", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}]}}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 18}}, {"id": 9, "title": "Step Command Success Rate", "description": "Success rate percentage of step command publishing (successful vs total).", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "(sum(orchestrator_step_commands_successful_Total_number_of_step_commands_successfully_published_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"}) / sum(orchestrator_step_commands_published_Total_number_of_step_commands_published_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"})) * 100", "instant": false, "legendFormat": "Success Rate", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}, "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 18}}, {"id": 10, "title": "Step Command Publishing by Step", "description": "Step command publishing rate by individual step version.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (step_version_name) (rate(orchestrator_step_commands_published_Total_number_of_step_commands_published_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"}[5m]))", "instant": false, "legendFormat": "{{step_version_name}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 26}}, {"id": 11, "title": "Flow Anomaly Detection", "description": "Real-time flow anomaly detection - shows difference between published commands and consumed events per step. Values > 0 indicate anomalies.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "abs(sum by (step_version_name) (orchestrator_step_commands_published_Total_number_of_step_commands_published_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"}) - sum by (step_version_name) (orchestrator_activity_events_consumed_Total_number_of_activity_events_consumed_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"}))", "instant": false, "legendFormat": "{{step_version_name}} Anomaly", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 26}}, {"id": 12, "title": "Activity Event Processing Metrics", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 34}, "collapsed": false}, {"id": 13, "title": "Activity Events Processed (Total)", "description": "Total number of activity events processed by the orchestrator.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(orchestrator_activity_events_consumed_Total_number_of_activity_events_consumed_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"})", "instant": false, "legendFormat": "Total Processed", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 35}}, {"id": 14, "title": "Activity Event Processing Rate", "description": "Rate of activity events being processed per second.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(rate(orchestrator_activity_events_consumed_Total_number_of_activity_events_consumed_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"}[5m]))", "instant": false, "legendFormat": "Events/sec", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}]}}}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 35}}, {"id": 15, "title": "Activity Event Success Rate", "description": "Success rate percentage of activity event processing (successful vs total).", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "(sum(orchestrator_activity_events_successful_Total_number_of_activity_events_successfully_processed_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"}) / sum(orchestrator_activity_events_consumed_Total_number_of_activity_events_consumed_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"})) * 100", "instant": false, "legendFormat": "Success Rate", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}, "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 35}}, {"id": 16, "title": "Activity Event Processing by Step", "description": "Activity event processing rate by individual step version.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (step_version_name) (rate(orchestrator_activity_events_consumed_Total_number_of_activity_events_consumed_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"}[5m]))", "instant": false, "legendFormat": "{{step_version_name}}", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 43}}, {"id": 17, "title": "Flow Balance Monitoring", "description": "Flow balance per step - shows published vs consumed counts. Perfect balance = equal values.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (step_version_name) (orchestrator_step_commands_published_Total_number_of_step_commands_published_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"})", "instant": false, "legendFormat": "{{step_version_name}} Published", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (step_version_name) (orchestrator_activity_events_consumed_Total_number_of_activity_events_consumed_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"})", "instant": false, "legendFormat": "{{step_version_name}} Consumed", "range": true, "refId": "B"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 43}}, {"id": 18, "title": "Active Step Executions & Cache Entries", "description": "Current number of active step executions and cache entries.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "orchestrator_active_step_executions_comprehensive_total_Current_number_of_active_step_executions_comprehensive{orchestrator_composite_key=~\"$orchestrator_composite_key\"}", "instant": false, "legendFormat": "Active Step Executions (Comprehensive)", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "orchestrator_active_cache_entries_total_Current_number_of_active_cache_entries{orchestrator_composite_key=~\"$orchestrator_composite_key\"}", "instant": false, "legendFormat": "Active Cache Entries", "range": true, "refId": "B"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 51}}, {"id": 19, "title": "Health Monitoring Metrics", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 59}, "collapsed": false}, {"id": 20, "title": "Health Checks Performed", "description": "Total number of health checks performed by component.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(orchestrator_health_checks_Total_number_of_health_checks_performed_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"})", "instant": false, "legendFormat": "Total Checks", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "blue", "value": 0}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 60}}, {"id": 21, "title": "Healthy Health Checks", "description": "Number of health checks with healthy status.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(orchestrator_health_checks_Total_number_of_health_checks_performed_total{orchestrator_composite_key=~\"$orchestrator_composite_key\", health_check_status=\"healthy\"})", "instant": false, "legendFormat": "Healthy", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}]}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 60}}, {"id": 22, "title": "Unhealthy Health Checks", "description": "Number of health checks with unhealthy status.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum(orchestrator_health_checks_Total_number_of_health_checks_performed_total{orchestrator_composite_key=~\"$orchestrator_composite_key\", health_check_status=\"unhealthy\"})", "instant": false, "legendFormat": "Unhealthy", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}]}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 60}}, {"id": 23, "title": "Health Check Success Rate", "description": "Percentage of health checks that are healthy.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "(sum(orchestrator_health_checks_Total_number_of_health_checks_performed_total{orchestrator_composite_key=~\"$orchestrator_composite_key\", health_check_status=\"healthy\"}) / sum(orchestrator_health_checks_Total_number_of_health_checks_performed_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"})) * 100", "instant": false, "legendFormat": "Success Rate", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}, "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 60}}, {"id": 24, "title": "Cache Operations by Type", "description": "Cache operations performed by operation type and status.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (operation_type, status) (rate(orchestrator_cache_operations_Total_number_of_cache_operations_performed_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"}[5m]))", "instant": false, "legendFormat": "{{operation_type}} ({{status}})", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 68}}, {"id": 25, "title": "Flow Anomaly Detection & Alerts", "type": "row", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 76}, "collapsed": false}, {"id": 26, "title": "Flow Anomaly Alert Status", "description": "Real-time flow anomaly detection status. Green = Healthy, Red = Anomaly Detected.", "type": "stat", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "max(abs(sum by (step_version_name) (orchestrator_step_commands_published_Total_number_of_step_commands_published_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"}) - sum by (step_version_name) (orchestrator_activity_events_consumed_Total_number_of_activity_events_consumed_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"})))", "instant": false, "legendFormat": "Max Anomaly", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}, "mappings": [{"options": {"0": {"text": "Healthy"}}, "type": "value"}, {"options": {"from": 1, "to": 999999, "result": {"text": "ANOMALY DETECTED"}}, "type": "range"}]}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 77}}, {"id": 27, "title": "Flow Balance Table", "description": "Detailed flow balance per step showing published vs consumed counts.", "type": "table", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (step_version_name) (orchestrator_step_commands_published_Total_number_of_step_commands_published_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"})", "instant": true, "legendFormat": "{{step_version_name}}", "range": false, "refId": "A"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (step_version_name) (orchestrator_activity_events_consumed_Total_number_of_activity_events_consumed_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"})", "instant": true, "legendFormat": "{{step_version_name}}", "range": false, "refId": "B"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "transformations": [{"id": "joinByField", "options": {"byField": "step_version_name", "mode": "outer"}}, {"id": "organize", "options": {"excludeByName": {"Time": true}, "renameByName": {"Value #A": "Published", "Value #B": "Consumed", "step_version_name": "Step"}}}, {"id": "calculateField", "options": {"alias": "Difference", "binary": {"left": "Published", "operator": "-", "right": "Consumed"}, "mode": "binary", "reduce": {"reducer": "sum"}}}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 77}}, {"id": 28, "title": "Health Check Status", "description": "Health check status by check name and status.", "type": "timeseries", "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "sum by (health_check_name, health_check_status) (rate(orchestrator_health_checks_Total_number_of_health_checks_performed_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"}[5m]))", "instant": false, "legendFormat": "{{health_check_name}} ({{health_check_status}})", "range": true, "refId": "A"}], "fieldConfig": {"defaults": {"unit": "ops", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 85}}], "refresh": "30s", "schemaVersion": 38, "style": "dark", "tags": ["orchestrator", "metrics", "monitoring", "workflow"], "templating": {"list": [{"name": "orchestrator_composite_key", "type": "query", "label": "Orchestrator", "description": "Select one or more orchestrators to monitor", "query": "label_values(orchestrator_health_status_comprehensive_Current_health_status_of_the_orchestrator_0_Healthy_1_Degraded_2_Unhealthy, orchestrator_composite_key)", "datasource": {"type": "prometheus"}, "refresh": 1, "sort": 1, "multi": true, "includeAll": true, "allValue": ".*", "current": {"selected": true, "text": "All", "value": "$__all"}, "options": [], "regex": "", "skipUrlSync": false}, {"name": "orchestrated_flow_version_name", "type": "query", "label": "Orchestrated Flow", "description": "Select one or more orchestrated flows to monitor", "query": "label_values(orchestrator_activity_events_consumed_Total_number_of_activity_events_consumed_by_the_orchestrator_total{orchestrator_composite_key=~\"$orchestrator_composite_key\"}, orchestrated_flow_version_name)", "datasource": {"type": "prometheus"}, "refresh": 1, "sort": 1, "multi": true, "includeAll": true, "allValue": ".*", "current": {"selected": true, "text": "All", "value": "$__all"}, "options": [], "regex": "", "skipUrlSync": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "Orchestrator Comprehensive Metrics Dashboard - Enhanced with Performance & Health Monitoring", "uid": null, "version": 1, "weekStart": ""}