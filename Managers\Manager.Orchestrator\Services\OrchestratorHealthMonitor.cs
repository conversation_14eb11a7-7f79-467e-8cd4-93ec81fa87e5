using System.Diagnostics;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Caching.Distributed;
using System.Text.Json;
using Shared.Correlation;
using Shared.Extensions;
using Shared.Services;
using Shared.Models;
using HealthStatus = Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus;
using ManagerConfiguration = Shared.Models.ManagerConfiguration;

namespace Manager.Orchestrator.Services;

/// <summary>
/// Comprehensive background service that monitors orchestrator health using the processor health monitoring pattern.
/// Implements orchestrator-centric health monitoring with rich data collection and cache-based distribution.
/// Multiple orchestrator instances can run this service without coordination using last-writer-wins strategy.
/// </summary>
public class OrchestratorHealthMonitor : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<OrchestratorHealthMonitor> _logger;
    private readonly OrchestratorHealthMonitoringConfig _config;
    private readonly ManagerConfiguration _managerConfig;
    private readonly IDistributedCache? _distributedCache;
    private readonly Guid _orchestratorId;
    private readonly Guid _instanceId;
    private readonly DateTime _startTime;

    private Timer? _healthCheckTimer;
    private readonly SemaphoreSlim _healthCheckSemaphore = new(1, 1);
    private long _totalHealthChecks = 0;
    private long _successfulHealthChecks = 0;
    private long _failedHealthChecks = 0;
    private DateTime _lastSuccessfulHealthCheck = DateTime.MinValue;
    private int _consecutiveFailures = 0;

    public OrchestratorHealthMonitor(
        IServiceProvider serviceProvider,
        IOptions<OrchestratorHealthMonitoringConfig> config,
        IOptions<ManagerConfiguration> managerConfig,
        ILogger<OrchestratorHealthMonitor> logger,
        IDistributedCache? distributedCache = null)
    {
        _serviceProvider = serviceProvider;
        _config = config.Value;
        _managerConfig = managerConfig.Value;
        _logger = logger;
        _distributedCache = distributedCache;
        _orchestratorId = Guid.NewGuid(); // Generate unique orchestrator ID
        _instanceId = Guid.NewGuid();
        _startTime = DateTime.UtcNow;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        if (!_config.Enabled)
        {
            _logger.LogInformationWithCorrelation("Orchestrator health monitoring is disabled");
            return;
        }

        _logger.LogInformationWithCorrelation(
            "Starting comprehensive orchestrator health monitoring. Interval: {Interval}, Manager: {ManagerName} v{ManagerVersion}, OrchestratorId: {OrchestratorId}, InstanceId: {InstanceId}",
            _config.HealthCheckInterval, _managerConfig.Name, _managerConfig.Version, _orchestratorId, _instanceId);

        // Perform initial health check immediately
        await PerformComprehensiveHealthCheckAsync();

        // Set up periodic health checks
        _healthCheckTimer = new Timer(
            async _ => await PerformComprehensiveHealthCheckAsync(),
            null,
            _config.HealthCheckInterval,
            _config.HealthCheckInterval);

        // Keep the service running
        while (!stoppingToken.IsCancellationRequested)
        {
            await Task.Delay(TimeSpan.FromSeconds(1), stoppingToken);
        }
    }

    /// <summary>
    /// Performs comprehensive health check following the processor health monitoring pattern.
    /// Collects rich health data, stores in cache, and records detailed metrics.
    /// </summary>
    public async Task PerformComprehensiveHealthCheckAsync()
    {
        // Local concurrency control - only one health check at a time
        if (!await _healthCheckSemaphore.WaitAsync(TimeSpan.FromSeconds(5)))
        {
            _logger.LogWarningWithCorrelation("Comprehensive health check already in progress, skipping cycle");
            return;
        }

        var healthCheckId = Guid.NewGuid();
        Interlocked.Increment(ref _totalHealthChecks);

        try
        {
            var stopwatch = Stopwatch.StartNew();

            _logger.LogDebugWithCorrelation(
                "🔍 Starting comprehensive orchestrator health check {HealthCheckId}. Manager: {ManagerName} v{ManagerVersion}, OrchestratorId: {OrchestratorId}",
                healthCheckId, _managerConfig.Name, _managerConfig.Version, _orchestratorId);

            // Perform comprehensive health checks and collect rich data using scoped services
            using var scope = _serviceProvider.CreateScope();
            var healthCheckService = scope.ServiceProvider.GetRequiredService<HealthCheckService>();
            var metricsService = scope.ServiceProvider.GetRequiredService<IOrchestratorMetricsService>();

            // Get orchestrator health status
            var healthReport = await GetOrchestratorHealthStatusAsync(healthCheckService);

            // Collect performance metrics if available
            OrchestratorPerformanceMetrics? performanceMetrics = null;
            try
            {
                var performanceService = scope.ServiceProvider.GetService<IOrchestratorPerformanceMetricsService>();
                if (performanceService != null)
                {
                    performanceMetrics = await performanceService.CollectPerformanceMetricsAsync();
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarningWithCorrelation(ex, "Failed to collect performance metrics");
            }

            // Create comprehensive health cache entry
            var healthEntry = await CreateHealthCacheEntryAsync(healthReport, performanceMetrics);

            // Store in distributed cache for other services to consume (if available)
            if (_distributedCache != null)
            {
                await StoreHealthCacheEntryAsync(healthEntry);
            }

            stopwatch.Stop();

            _logger.LogDebugWithCorrelation(
                "🔥 DEBUG: Comprehensive health checks completed in {Duration}ms. Overall: {OverallStatus}, Individual checks: {CheckCount}, OrchestratorId: {OrchestratorId}",
                stopwatch.ElapsedMilliseconds, healthReport.Status, healthReport.Entries.Count, _orchestratorId);

            // Always record comprehensive health cache entry metrics for analysis
            await RecordHealthCacheEntryMetricsAsync(metricsService, healthEntry);

            // Record cache statistics
            await RecordCacheStatisticsAsync(scope.ServiceProvider);

            Interlocked.Increment(ref _successfulHealthChecks);
            _lastSuccessfulHealthCheck = DateTime.UtcNow;
            _consecutiveFailures = 0; // Reset on success

            if (_config.LogHealthCheckResults)
            {
                LogComprehensiveHealthCheckResult(healthCheckId, healthEntry, stopwatch.Elapsed);
            }
        }
        catch (Exception ex)
        {
            Interlocked.Increment(ref _failedHealthChecks);
            _consecutiveFailures++;

            _logger.LogErrorWithCorrelation(ex, "Failed to perform comprehensive orchestrator health check {HealthCheckId}. Consecutive failures: {Failures}", healthCheckId, _consecutiveFailures);

            // Record unhealthy status on failure using scoped service
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var metricsService = scope.ServiceProvider.GetRequiredService<IOrchestratorMetricsService>();
                metricsService.RecordHealthStatus(2); // 2 = Unhealthy
            }
            catch (Exception metricsEx)
            {
                _logger.LogWarningWithCorrelation(metricsEx, "Failed to record unhealthy status metric");
            }

            if (!_config.ContinueOnUninitialized)
            {
                throw;
            }
        }
        finally
        {
            _healthCheckSemaphore.Release();
        }
    }

    /// <summary>
    /// Legacy method for backward compatibility.
    /// </summary>
    public async Task PerformHealthCheckAsync()
    {
        await PerformComprehensiveHealthCheckAsync();
    }

    /// <summary>
    /// Gets orchestrator health status from .NET health checks.
    /// </summary>
    private async Task<HealthReport> GetOrchestratorHealthStatusAsync(HealthCheckService healthCheckService)
    {
        try
        {
            var healthReport = await healthCheckService.CheckHealthAsync();
            return healthReport;
        }
        catch (Exception ex)
        {
            _logger.LogErrorWithCorrelation(ex, "Failed to get orchestrator health status");

            // Return unhealthy status on failure
            var entries = new Dictionary<string, HealthReportEntry>
            {
                ["health-check-failure"] = new HealthReportEntry(
                    HealthStatus.Unhealthy,
                    "Health check execution failed",
                    TimeSpan.Zero,
                    ex,
                    new Dictionary<string, object>())
            };

            return new HealthReport(entries, TimeSpan.Zero);
        }
    }

    /// <summary>
    /// Creates comprehensive health cache entry with all collected data.
    /// </summary>
    private async Task<OrchestratorHealthCacheEntry> CreateHealthCacheEntryAsync(
        HealthReport healthReport,
        OrchestratorPerformanceMetrics? performanceMetrics)
    {
        var healthEntry = new OrchestratorHealthCacheEntry
        {
            OrchestratorId = _orchestratorId,
            InstanceId = _instanceId,
            Status = ConvertHealthStatus(healthReport.Status),
            LastUpdated = DateTime.UtcNow,
            ExpiresAt = DateTime.UtcNow.Add(_config.HealthCacheTtl),
            ReportingPodId = Environment.MachineName,
            Message = GetHealthMessage(healthReport),
            Uptime = DateTime.UtcNow - _startTime,
            Metadata = new OrchestratorMetadata
            {
                ProcessId = Environment.ProcessId,
                HostName = System.Environment.MachineName,
                StartTime = _startTime,
                Version = _managerConfig.Version,
                Name = _managerConfig.Name,
                Environment = "Production" // Could be configurable
            },
            PerformanceMetrics = performanceMetrics ?? new OrchestratorPerformanceMetrics(),
            OrchestrationMetrics = new OrchestrationMetrics()
        };

        // Include detailed health checks - convert from Microsoft types to our types
        healthEntry.HealthChecks = healthReport.Entries.ToDictionary(
            kvp => kvp.Key,
            kvp => new Shared.Models.HealthCheckResult
            {
                Status = ConvertHealthStatus(kvp.Value.Status),
                Description = kvp.Value.Description ?? string.Empty,
                Exception = kvp.Value.Exception?.ToString(),
                Data = kvp.Value.Data?.ToDictionary(d => d.Key, d => d.Value) ?? new Dictionary<string, object>(),
                Duration = kvp.Value.Duration
            });

        await Task.CompletedTask;
        return healthEntry;
    }

    /// <summary>
    /// Converts Microsoft HealthStatus to our custom HealthStatus.
    /// </summary>
    private Shared.Models.HealthStatus ConvertHealthStatus(Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus status)
    {
        return status switch
        {
            Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Healthy => Shared.Models.HealthStatus.Healthy,
            Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Degraded => Shared.Models.HealthStatus.Degraded,
            Microsoft.Extensions.Diagnostics.HealthChecks.HealthStatus.Unhealthy => Shared.Models.HealthStatus.Unhealthy,
            _ => Shared.Models.HealthStatus.Unhealthy
        };
    }

    /// <summary>
    /// Gets human-readable health message from health report.
    /// </summary>
    private string GetHealthMessage(HealthReport healthReport)
    {
        if (healthReport.Status == HealthStatus.Healthy)
        {
            return "All health checks passed";
        }

        var failedChecks = healthReport.Entries
            .Where(e => e.Value.Status != HealthStatus.Healthy)
            .Select(e => $"{e.Key}: {e.Value.Status}")
            .ToList();

        return failedChecks.Any()
            ? $"Failed checks: {string.Join(", ", failedChecks)}"
            : "Health check issues detected";
    }

    /// <summary>
    /// Stores health cache entry in distributed cache.
    /// </summary>
    private async Task StoreHealthCacheEntryAsync(OrchestratorHealthCacheEntry healthEntry)
    {
        try
        {
            var cacheKey = _config.GetCacheKey(healthEntry.OrchestratorId);
            var cacheOptions = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = _config.HealthCacheTtl
            };

            var json = JsonSerializer.Serialize(healthEntry);
            await _distributedCache!.SetStringAsync(cacheKey, json, cacheOptions);

            _logger.LogDebugWithCorrelation("Stored health cache entry for orchestrator {OrchestratorId}", healthEntry.OrchestratorId);
        }
        catch (Exception ex)
        {
            _logger.LogWarningWithCorrelation(ex, "Failed to store health cache entry for orchestrator {OrchestratorId}", healthEntry.OrchestratorId);
        }
    }

    /// <summary>
    /// Records comprehensive metrics from health cache entry.
    /// </summary>
    private async Task RecordHealthCacheEntryMetricsAsync(IOrchestratorMetricsService metricsService, OrchestratorHealthCacheEntry healthEntry)
    {
        try
        {
            // Use the comprehensive health cache entry metrics method
            metricsService.RecordHealthCacheEntryMetrics(healthEntry);

            // Record individual health checks for backward compatibility
            foreach (var healthCheck in healthEntry.HealthChecks)
            {
                var healthCheckStatus = healthCheck.Value.Status switch
                {
                    Shared.Models.HealthStatus.Healthy => "healthy",
                    Shared.Models.HealthStatus.Degraded => "degraded",
                    Shared.Models.HealthStatus.Unhealthy => "unhealthy",
                    _ => "unknown"
                };

                metricsService.RecordHealthCheck(healthCheck.Key, healthCheckStatus);

                _logger.LogDebugWithCorrelation(
                    "🔥 DEBUG: Recorded comprehensive health check metric - Name: {HealthCheckName}, Status: {Status}, Duration: {Duration}ms",
                    healthCheck.Key, healthCheckStatus, healthCheck.Value.Duration.TotalMilliseconds);
            }

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogWarningWithCorrelation(ex, "Failed to record health cache entry metrics");
        }
    }

    private async Task RecordCacheStatisticsAsync(IServiceProvider serviceProvider)
    {
        try
        {
            var hazelcastCacheService = serviceProvider.GetRequiredService<ICacheService>();
            var metricsService = serviceProvider.GetRequiredService<IOrchestratorMetricsService>();

            // Get cache statistics from Hazelcast - use orchestration cache map
            var cacheMapName = "orchestration-cache"; // Default orchestration cache map
            var (entryCount, averageAge) = await hazelcastCacheService.GetCacheStatisticsAsync(cacheMapName);

            // Update active cache entries metric
            metricsService.UpdateActiveCacheEntries(entryCount);

            _logger.LogDebugWithCorrelation(
                "🔥 DEBUG: Recorded orchestrator cache metrics - EntryCount: {EntryCount}, AverageAge: {AverageAge}s, " +
                "Manager: {ManagerName} v{ManagerVersion}, CacheMap: {CacheMapName}, OrchestratorId: {OrchestratorId}",
                entryCount, averageAge, _managerConfig.Name, _managerConfig.Version, cacheMapName, _orchestratorId);
        }
        catch (Exception ex)
        {
            _logger.LogWarningWithCorrelation(ex,
                "Failed to collect orchestrator cache statistics for metrics. Manager: {ManagerName} v{ManagerVersion}, OrchestratorId: {OrchestratorId}",
                _managerConfig.Name, _managerConfig.Version, _orchestratorId);
        }
    }

    private void LogComprehensiveHealthCheckResult(Guid healthCheckId, OrchestratorHealthCacheEntry healthEntry, TimeSpan duration)
    {
        var logLevel = LogLevel.Information; // Default to Information level

        var successRate = _totalHealthChecks > 0 ? (_successfulHealthChecks * 100.0) / _totalHealthChecks : 100.0;
        var failureRate = _totalHealthChecks > 0 ? (_failedHealthChecks * 100.0) / _totalHealthChecks : 0.0;

        // Get individual health check details
        var healthCheckDetails = string.Join(", ", healthEntry.HealthChecks.Select(e =>
            $"{e.Key}:{e.Value.Status}({e.Value.Duration.TotalMilliseconds:F0}ms)"));

        _logger.Log(logLevel,
            "🏥 Comprehensive Orchestrator Health Check {HealthCheckId} completed. " +
            "Overall: {OverallStatus}, Duration: {Duration}ms, Manager: {ManagerName} v{ManagerVersion}, " +
            "OrchestratorId: {OrchestratorId}, InstanceId: {InstanceId}, " +
            "CPU: {CpuUsage:F2}%, Memory: {MemoryMB:F2}MB, Uptime: {Uptime}, " +
            "Checks: [{HealthCheckDetails}], " +
            "Stats: Success={SuccessRate:F1}% ({SuccessfulChecks}/{TotalChecks}), " +
            "Failed={FailureRate:F1}% ({FailedChecks}), ConsecutiveFailures: {ConsecutiveFailures}, LastSuccess: {LastSuccessTime}",
            healthCheckId,
            healthEntry.Status,
            duration.TotalMilliseconds,
            _managerConfig.Name,
            _managerConfig.Version,
            _orchestratorId,
            _instanceId,
            healthEntry.PerformanceMetrics.CpuUsagePercent,
            healthEntry.PerformanceMetrics.MemoryUsageMB,
            healthEntry.Uptime.ToString(@"dd\.hh\:mm\:ss"),
            healthCheckDetails,
            successRate,
            _successfulHealthChecks,
            _totalHealthChecks,
            failureRate,
            _failedHealthChecks,
            _consecutiveFailures,
            _lastSuccessfulHealthCheck == DateTime.MinValue ? "Never" : _lastSuccessfulHealthCheck.ToString("HH:mm:ss"));
    }

    /// <summary>
    /// Legacy method for backward compatibility.
    /// </summary>
    private void LogHealthCheckResult(Guid healthCheckId, HealthReport healthReport, TimeSpan duration)
    {
        // Create a basic health entry for legacy logging
        var healthEntry = new OrchestratorHealthCacheEntry
        {
            OrchestratorId = _orchestratorId,
            InstanceId = _instanceId,
            Status = ConvertHealthStatus(healthReport.Status),
            HealthChecks = healthReport.Entries.ToDictionary(
                kvp => kvp.Key,
                kvp => new Shared.Models.HealthCheckResult
                {
                    Status = ConvertHealthStatus(kvp.Value.Status),
                    Description = kvp.Value.Description ?? string.Empty,
                    Exception = kvp.Value.Exception?.ToString(),
                    Data = kvp.Value.Data?.ToDictionary(d => d.Key, d => d.Value) ?? new Dictionary<string, object>(),
                    Duration = kvp.Value.Duration
                }),
            PerformanceMetrics = new OrchestratorPerformanceMetrics(),
            Uptime = DateTime.UtcNow - _startTime
        };

        LogComprehensiveHealthCheckResult(healthCheckId, healthEntry, duration);
    }

    public override void Dispose()
    {
        _healthCheckTimer?.Dispose();
        _healthCheckSemaphore?.Dispose();
        base.Dispose();
    }
}

/// <summary>
/// Configuration for orchestrator health monitoring following processor pattern
/// </summary>
public class OrchestratorHealthMonitorConfiguration
{
    /// <summary>
    /// Whether health monitoring is enabled
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Interval between health checks
    /// </summary>
    public TimeSpan HealthCheckInterval { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Cache map name for orchestration data
    /// </summary>
    public string CacheMapName { get; set; } = "orchestration-cache";

    /// <summary>
    /// Whether to log health check results
    /// </summary>
    public bool LogHealthChecks { get; set; } = true;

    /// <summary>
    /// Log level for health check logging (Information, Warning, Error, Debug)
    /// </summary>
    public string LogLevel { get; set; } = "Information";

    /// <summary>
    /// Whether to continue on health check failures
    /// </summary>
    public bool ContinueOnFailure { get; set; } = true;

    /// <summary>
    /// Maximum number of retries for cache operations
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// Delay between retry attempts
    /// </summary>
    public TimeSpan RetryDelay { get; set; } = TimeSpan.FromSeconds(1);

    /// <summary>
    /// Whether to use exponential backoff for retries
    /// </summary>
    public bool UseExponentialBackoff { get; set; } = true;
}
