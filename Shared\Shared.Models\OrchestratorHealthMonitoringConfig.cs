using System.ComponentModel.DataAnnotations;

namespace Shared.Models;

/// <summary>
/// Configuration for orchestrator health monitoring.
/// Matches the processor health monitoring configuration pattern.
/// </summary>
public class OrchestratorHealthMonitoringConfig
{
    /// <summary>
    /// Configuration section name in appsettings.json
    /// </summary>
    public const string SectionName = "OrchestratorHealthMonitoring";

    /// <summary>
    /// Whether health monitoring is enabled.
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Interval between health checks.
    /// </summary>
    [Range(typeof(TimeSpan), "00:00:01", "01:00:00")]
    public TimeSpan HealthCheckInterval { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// How long health cache entries remain valid.
    /// </summary>
    [Range(typeof(TimeSpan), "00:00:30", "01:00:00")]
    public TimeSpan HealthCacheTtl { get; set; } = TimeSpan.FromMinutes(5);

    /// <summary>
    /// Whether to include detailed health check results in cache entries.
    /// </summary>
    public bool IncludeDetailedHealthChecks { get; set; } = true;

    /// <summary>
    /// Whether to collect performance metrics (CPU, memory).
    /// </summary>
    public bool CollectPerformanceMetrics { get; set; } = true;

    /// <summary>
    /// Whether to collect orchestration-specific metrics.
    /// </summary>
    public bool CollectOrchestrationMetrics { get; set; } = true;

    /// <summary>
    /// Unique identifier for this orchestrator instance.
    /// If not provided, a new GUID will be generated.
    /// </summary>
    public Guid? OrchestratorId { get; set; }

    /// <summary>
    /// Unique identifier for this pod/instance.
    /// Used for debugging in distributed environments.
    /// </summary>
    public string PodId { get; set; } = string.Empty;

    /// <summary>
    /// Name of the orchestrator manager.
    /// </summary>
    public string OrchestratorName { get; set; } = "Orchestrator Manager";

    /// <summary>
    /// Version of the orchestrator manager.
    /// </summary>
    public string OrchestratorVersion { get; set; } = "1.0.0";

    /// <summary>
    /// Environment where the orchestrator is running.
    /// </summary>
    public string Environment { get; set; } = "Development";

    /// <summary>
    /// Cache key prefix for orchestrator health entries.
    /// </summary>
    public string CacheKeyPrefix { get; set; } = "orchestrator:health";

    /// <summary>
    /// Timeout for health check operations.
    /// </summary>
    [Range(typeof(TimeSpan), "00:00:01", "00:05:00")]
    public TimeSpan HealthCheckTimeout { get; set; } = TimeSpan.FromSeconds(30);

    /// <summary>
    /// Whether to continue health monitoring even if the orchestrator is not fully initialized.
    /// </summary>
    public bool ContinueOnUninitialized { get; set; } = true;

    /// <summary>
    /// Maximum number of health check failures before marking as unhealthy.
    /// </summary>
    [Range(1, 10)]
    public int MaxConsecutiveFailures { get; set; } = 3;

    /// <summary>
    /// Whether to log health check results.
    /// </summary>
    public bool LogHealthCheckResults { get; set; } = true;

    /// <summary>
    /// Whether to record health metrics to Prometheus/OpenTelemetry.
    /// </summary>
    public bool RecordHealthMetrics { get; set; } = true;

    /// <summary>
    /// Gets the cache key for a specific orchestrator instance.
    /// </summary>
    /// <param name="orchestratorId">The orchestrator ID</param>
    /// <returns>The cache key</returns>
    public string GetCacheKey(Guid orchestratorId)
    {
        return $"{CacheKeyPrefix}:{orchestratorId}";
    }

    /// <summary>
    /// Gets the orchestrator ID, generating a new one if not configured.
    /// </summary>
    /// <returns>The orchestrator ID</returns>
    public Guid GetOrchestratorId()
    {
        return OrchestratorId ?? Guid.NewGuid();
    }

    /// <summary>
    /// Validates the configuration.
    /// </summary>
    /// <returns>True if valid, false otherwise</returns>
    public bool IsValid()
    {
        return HealthCheckInterval > TimeSpan.Zero &&
               HealthCacheTtl > TimeSpan.Zero &&
               HealthCheckTimeout > TimeSpan.Zero &&
               MaxConsecutiveFailures > 0 &&
               !string.IsNullOrWhiteSpace(PodId) &&
               !string.IsNullOrWhiteSpace(OrchestratorName) &&
               !string.IsNullOrWhiteSpace(OrchestratorVersion) &&
               !string.IsNullOrWhiteSpace(Environment) &&
               !string.IsNullOrWhiteSpace(CacheKeyPrefix);
    }
}
