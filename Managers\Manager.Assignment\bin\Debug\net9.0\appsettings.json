﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "MassTransit": "Information",
      "MongoDB": "Information"
    }
  },
  "AllowedHosts": "*",
  "ManagerConfiguration": {
    "Version": "1.0.0",
    "Name": "AssignmentManager",
    "Description": "Manager for assignment entities and operations"
  },
  "ConnectionStrings": {
    "MongoDB": "mongodb://localhost:27017"
  },
  "MongoDB": {
    "DatabaseName": "ManagerAssignmentDB"
  },
  "RabbitMQ": {
    "Host": "localhost",
    "VirtualHost": "/",
    "Username": "guest",
    "Password": "guest"
  },
  "OpenTelemetry": {
    "Endpoint": "http://localhost:4317",
    "UseConsoleInDevelopment": false,
    "ServiceName": "AssignmentManager",
    "ServiceVersion": "1.0.0"
  },
  "Features": {
    "ReferentialIntegrityValidation": true
  },
  "ReferentialIntegrity": {
    "ValidationTimeoutMs": 3000,
    "EnableParallelValidation": true,
    "ValidateSourceReferences": true,
    "ValidateDestinationReferences": true,
    "ValidateAssignmentReferences": true,
    "ValidateScheduledFlowReferences": true,
    "ValidateStepReferences": true,
    "ValidateFlowReferences": true
  },
  "Services": {
    "StepManager": {
      "BaseUrl": "http://localhost:5000"
    }
  }
}
