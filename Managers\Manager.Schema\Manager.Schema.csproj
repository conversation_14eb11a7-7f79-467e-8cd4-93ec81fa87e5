﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <ProjectGuid>{86D2DE08-A816-47E8-A052-D5BA3B98203C}</ProjectGuid>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>Manager.Schema</RootNamespace>
    <AssemblyName>Manager.Schema</AssemblyName>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.HealthChecks.MongoDb" Version="8.0.1" />
    <PackageReference Include="AspNetCore.HealthChecks.Rabbitmq" Version="8.0.2" />
    <PackageReference Include="MassTransit" Version="8.2.5" />
    <PackageReference Include="MassTransit.RabbitMQ" Version="8.2.5" />
    <PackageReference Include="MongoDB.Driver" Version="2.22.0" />
    <PackageReference Include="OpenTelemetry" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.12.0" />
    <PackageReference Include="OpenTelemetry.Exporter.Console" Version="1.12.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.8.1" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.8.1" />
    <PackageReference Include="System.Diagnostics.DiagnosticSource" Version="9.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="9.0.0" />
    <PackageReference Include="Polly.Extensions.Http" Version="3.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="**\*.cs" Exclude="bin\**;obj\**" />
    <Compile Include="..\Shared\**\*.cs" Exclude="..\Shared\bin\**;..\Shared\obj\**;..\Shared\Exceptions\**;..\Shared\MassTransit\**;..\Shared\Entities\**;..\Shared\Repositories\**;..\Shared\Configuration\**;..\Shared\MongoDB\**" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Shared\Shared.Configuration\Shared.Configuration.csproj" />
    <ProjectReference Include="..\..\Shared\Shared.Correlation\Shared.Correlation.csproj" />
    <ProjectReference Include="..\..\Shared\Shared.Entities\Shared.Entities.csproj" />
    <ProjectReference Include="..\..\Shared\Shared.Exceptions\Shared.Exceptions.csproj" />
    <ProjectReference Include="..\..\Shared\Shared.HealthChecks\Shared.HealthChecks.csproj" />
    <ProjectReference Include="..\..\Shared\Shared.MassTransit\Shared.MassTransit.csproj" />
    <ProjectReference Include="..\..\Shared\Shared.MongoDB\Shared.MongoDB.csproj" />
    <ProjectReference Include="..\..\Shared\Shared.Repositories\Shared.Repositories.csproj" />
    <ProjectReference Include="..\..\Shared\Shared.Services\Shared.Services.csproj" />
  </ItemGroup>

  <PropertyGroup>
    <ProjectGuid>{86D2DE08-A816-47E8-A052-D5BA3B98203C}</ProjectGuid>
    <EnableDefaultCompileItems>false</EnableDefaultCompileItems>
    <EnableDefaultContentItems>false</EnableDefaultContentItems>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="appsettings.Development.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="appsettings.Production.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
