{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "MassTransit": "Information", "Hazelcast": "Information"}}, "AllowedHosts": "*", "ManagerConfiguration": {"Version": "1.0.0", "Name": "Orchestrator<PERSON><PERSON><PERSON>", "Description": "Manager for orchestration operations and coordination"}, "RabbitMQ": {"Host": "localhost", "VirtualHost": "/", "Username": "guest", "Password": "guest", "RetryLimit": 3, "RetryInterval": "00:00:30", "PrefetchCount": 16, "ConcurrencyLimit": 10}, "Hazelcast": {"ClusterName": "EntitiesManager", "NetworkConfig": {"Addresses": ["127.0.0.1:5701"]}, "ConnectionTimeout": "00:00:30", "ConnectionRetryConfig": {"InitialBackoffMillis": 1000, "MaxBackoffMillis": 30000, "Multiplier": 2.0, "ClusterConnectTimeoutMillis": 20000, "JitterRatio": 0.2}}, "OpenTelemetry": {"Endpoint": "http://localhost:4317", "UseConsoleInDevelopment": false, "ServiceName": "Orchestrator<PERSON><PERSON><PERSON>", "ServiceVersion": "1.0.0"}, "ManagerUrls": {"Step": "http://localhost:5000", "Assignment": "http://localhost:5010", "Address": "http://localhost:5120", "Delivery": "http://localhost:5130", "Schema": "http://localhost:5100", "OrchestratedFlow": "http://localhost:5040", "Workflow": "http://localhost:5030"}, "OrchestrationCache": {"MapName": "orchestration-data", "DefaultTtlMinutes": 240, "RefreshThresholdMinutes": 30, "EnableSlidingExpiration": true, "EnableEndlessWorkflows": true, "EndlessWorkflowTtlDays": 30, "MaxRetries": 3, "RetryDelayMs": 1000}, "OrchestratorHealthMonitor": {"Enabled": true, "HealthCheckInterval": "00:00:30", "LogHealthChecks": true, "LogLevel": "Information", "ContinueOnFailure": true, "MaxRetries": 3, "RetryDelay": "00:00:01", "UseExponentialBackoff": true}, "HttpClient": {"TimeoutSeconds": 30, "MaxRetries": 3, "RetryDelayMs": 1000}}