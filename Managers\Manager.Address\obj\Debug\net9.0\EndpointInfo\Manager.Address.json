{"openapi": "3.0.1", "info": {"title": "Manager.Address", "version": "1.0"}, "paths": {"/api/Address": {"get": {"tags": ["Address"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AddressEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AddressEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AddressEntity"}}}}}}}, "post": {"tags": ["Address"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddressEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddressEntity"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddressEntity"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AddressEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddressEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddressEntity"}}}}}}}, "/api/Address/paged": {"get": {"tags": ["Address"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK"}}}}, "/api/Address/{id}": {"get": {"tags": ["Address"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AddressEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddressEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddressEntity"}}}}}}, "put": {"tags": ["Address"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddressEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddressEntity"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddressEntity"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AddressEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddressEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddressEntity"}}}}}}, "delete": {"tags": ["Address"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Address/composite/{version}/{name}/{connectionString}": {"get": {"tags": ["Address"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "connectionString", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AddressEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddressEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddressEntity"}}}}}}}, "/api/Address/version/{version}": {"get": {"tags": ["Address"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AddressEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AddressEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AddressEntity"}}}}}}}}, "/api/Address/name/{name}": {"get": {"tags": ["Address"], "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AddressEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AddressEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AddressEntity"}}}}}}}}, "/api/Address/composite": {"get": {"tags": ["Address"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AddressEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AddressEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddressEntity"}}}}}}}, "/api/Address/schema/{schemaId}/exists": {"get": {"tags": ["Address"], "parameters": [{"name": "schemaId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}}, "components": {"schemas": {"AddressEntity": {"required": ["connectionString", "name", "schemaId", "version"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "version": {"maxLength": 50, "minLength": 0, "pattern": "^\\d+\\.\\d+\\.\\d+$", "type": "string"}, "name": {"maxLength": 50, "minLength": 0, "type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "nullable": true}, "updatedBy": {"type": "string", "nullable": true}, "description": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}, "isNew": {"type": "boolean", "readOnly": true}, "connectionString": {"minLength": 1, "type": "string"}, "configuration": {"type": "object", "additionalProperties": {}, "nullable": true}, "schemaId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}}}}