[{"ContainingType": "Manager.Schema.Controllers.SchemaController", "Method": "GetAll", "RelativePath": "api/Schema", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.SchemaEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Schema.Controllers.SchemaController", "Method": "Create", "RelativePath": "api/Schema", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "entity", "Type": "Shared.Entities.SchemaEntity", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Entities.SchemaEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Schema.Controllers.SchemaController", "Method": "GetById", "RelativePath": "api/Schema/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Entities.SchemaEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Schema.Controllers.SchemaController", "Method": "Update", "RelativePath": "api/Schema/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "entity", "Type": "Shared.Entities.SchemaEntity", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Entities.SchemaEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Schema.Controllers.SchemaController", "Method": "Delete", "RelativePath": "api/Schema/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Manager.Schema.Controllers.SchemaController", "Method": "Exists", "RelativePath": "api/Schema/{id}/exists", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Schema.Controllers.SchemaController", "Method": "GetByCompositeKeyEmpty", "RelativePath": "api/Schema/composite", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Shared.Entities.SchemaEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Schema.Controllers.SchemaController", "Method": "GetByCompositeKey", "RelativePath": "api/Schema/composite/{version}/{name}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "version", "Type": "System.String", "IsRequired": true}, {"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Entities.SchemaEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Schema.Controllers.SchemaController", "Method": "GetByDefinition", "RelativePath": "api/Schema/definition/{definition}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "definition", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.SchemaEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Schema.Controllers.SchemaController", "Method": "GetByName", "RelativePath": "api/Schema/name/{name}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.SchemaEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Schema.Controllers.SchemaController", "Method": "GetPaged", "RelativePath": "api/Schema/paged", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Manager.Schema.Controllers.SchemaController", "Method": "GetByVersion", "RelativePath": "api/Schema/version/{version}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "version", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.SchemaEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]