﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "MassTransit": "Information",
      "MongoDB": "Information"
    }
  },
  "AllowedHosts": "*",
  "ManagerConfiguration": {
    "Version": "1.0.0",
    "Name": "SchemaManager",
    "Description": "Manager for schema entities and operations"
  },
  "ConnectionStrings": {
    "MongoDB": "mongodb://localhost:27017"
  },
  "MongoDB": {
    "DatabaseName": "ManagerSchemaDB"
  },
  "RabbitMQ": {
    "Host": "localhost",
    "VirtualHost": "/",
    "Username": "guest",
    "Password": "guest"
  },
  "OpenTelemetry": {
    "Endpoint": "http://localhost:4317",
    "UseConsoleInDevelopment": false,
    "ServiceName": "SchemaManager",
    "ServiceVersion": "1.0.0"
  },
  "Features": {
    "ReferentialIntegrityValidation": true
  },
  "ReferentialIntegrity": {
    "ValidationTimeoutMs": 3000,
    "EnableParallelValidation": true,
    "ValidateSourceReferences": true,
    "ValidateDestinationReferences": true,
    "ValidateAssignmentReferences": true,
    "ValidateScheduledFlowReferences": true,
    "ValidateStepReferences": true,
    "ValidateFlowReferences": true
  },
  "ManagerUrls": {
    "Address": "http://localhost:5120",
    "Delivery": "http://localhost:5130",
    "Processor": "http://localhost:5110"
  }
}
