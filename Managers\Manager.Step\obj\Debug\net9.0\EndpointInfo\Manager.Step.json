{"openapi": "3.0.1", "info": {"title": "Manager.Step", "version": "1.0"}, "paths": {"/api/Step": {"get": {"tags": ["Step"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StepEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StepEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StepEntity"}}}}}}}, "post": {"tags": ["Step"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StepEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StepEntity"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StepEntity"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StepEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StepEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StepEntity"}}}}}}}, "/api/Step/paged": {"get": {"tags": ["Step"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK"}}}}, "/api/Step/{id}": {"get": {"tags": ["Step"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StepEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StepEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StepEntity"}}}}}}, "put": {"tags": ["Step"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StepEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StepEntity"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StepEntity"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StepEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StepEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StepEntity"}}}}}}, "delete": {"tags": ["Step"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Step/composite/{version}/{name}": {"get": {"tags": ["Step"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StepEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StepEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StepEntity"}}}}}}}, "/api/Step/processor/{processorId}": {"get": {"tags": ["Step"], "parameters": [{"name": "processorId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StepEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StepEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StepEntity"}}}}}}}}, "/api/Step/processor/{processorId}/exists": {"get": {"tags": ["Step"], "parameters": [{"name": "processorId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/Step/nextstep/{stepId}": {"get": {"tags": ["Step"], "parameters": [{"name": "stepId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StepEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StepEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StepEntity"}}}}}}}}, "/api/Step/entrycondition/{condition}": {"get": {"tags": ["Step"], "parameters": [{"name": "condition", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StepEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StepEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StepEntity"}}}}}}}}, "/api/Step/{stepId}/exists": {"get": {"tags": ["Step"], "parameters": [{"name": "stepId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/Step/version/{version}": {"get": {"tags": ["Step"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StepEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StepEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StepEntity"}}}}}}}}, "/api/Step/name/{name}": {"get": {"tags": ["Step"], "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StepEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StepEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/StepEntity"}}}}}}}}, "/api/Step/composite": {"get": {"tags": ["Step"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/StepEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StepEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StepEntity"}}}}}}}}, "components": {"schemas": {"StepEntity": {"required": ["entryCondition", "name", "processorId", "version"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "version": {"maxLength": 50, "minLength": 0, "pattern": "^\\d+\\.\\d+\\.\\d+$", "type": "string"}, "name": {"maxLength": 50, "minLength": 0, "type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "nullable": true}, "updatedBy": {"type": "string", "nullable": true}, "description": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}, "isNew": {"type": "boolean", "readOnly": true}, "processorId": {"type": "string", "format": "uuid"}, "nextStepIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "entryCondition": {"$ref": "#/components/schemas/StepEntryCondition"}}, "additionalProperties": false}, "StepEntryCondition": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}}}}