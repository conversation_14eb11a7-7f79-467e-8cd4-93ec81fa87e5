<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Shared.MongoDB</name>
    </assembly>
    <members>
        <member name="T:Shared.MongoDB.JsonElementSerializer">
            <summary>
            Custom serializer for JsonElement to handle JSON objects in MongoDB.
            Provides bidirectional conversion between System.Text.Json.JsonElement and BSON types.
            </summary>
        </member>
        <member name="M:Shared.MongoDB.JsonElementSerializer.Deserialize(MongoDB.Bson.Serialization.BsonDeserializationContext,MongoDB.Bson.Serialization.BsonDeserializationArgs)">
            <summary>
            Deserializes a BSON value to a JsonElement.
            </summary>
            <param name="context">The deserialization context.</param>
            <param name="args">The deserialization arguments.</param>
            <returns>A JsonElement representing the BSON value.</returns>
        </member>
        <member name="M:Shared.MongoDB.JsonElementSerializer.Serialize(MongoDB.Bson.Serialization.BsonSerializationContext,MongoDB.Bson.Serialization.BsonSerializationArgs,System.Text.Json.JsonElement)">
            <summary>
            Serializes a JsonElement to BSON.
            </summary>
            <param name="context">The serialization context.</param>
            <param name="args">The serialization arguments.</param>
            <param name="value">The JsonElement to serialize.</param>
        </member>
        <member name="T:Shared.MongoDB.BsonConfiguration">
            <summary>
            Provides configuration for BSON serialization in MongoDB.
            Configures serializers, ID generators, and class maps for entities.
            </summary>
        </member>
        <member name="M:Shared.MongoDB.BsonConfiguration.Configure">
            <summary>
            Configures BSON serialization settings for MongoDB.
            This method is thread-safe and will only configure settings once.
            </summary>
        </member>
        <member name="T:Shared.MongoDB.GuidGenerator">
            <summary>
            Custom GUID generator for MongoDB entity IDs.
            Generates new GUIDs for entity creation and provides empty ID validation.
            </summary>
        </member>
        <member name="M:Shared.MongoDB.GuidGenerator.GenerateId(System.Object,System.Object)">
            <summary>
            Generates a new GUID for an entity ID.
            </summary>
            <param name="container">The container object (not used).</param>
            <param name="document">The document being created (not used).</param>
            <returns>A new GUID.</returns>
        </member>
        <member name="M:Shared.MongoDB.GuidGenerator.IsEmpty(System.Object)">
            <summary>
            Determines whether the provided ID is empty.
            </summary>
            <param name="id">The ID to check.</param>
            <returns>True if the ID is null or an empty GUID; otherwise, false.</returns>
        </member>
    </members>
</doc>
