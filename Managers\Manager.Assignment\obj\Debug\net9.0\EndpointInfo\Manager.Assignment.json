{"openapi": "3.0.1", "info": {"title": "Manager.Assignment", "version": "1.0"}, "paths": {"/api/Assignment": {"get": {"tags": ["Assignment"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AssignmentEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AssignmentEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AssignmentEntity"}}}}}}}, "post": {"tags": ["Assignment"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignmentEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignmentEntity"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AssignmentEntity"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AssignmentEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AssignmentEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignmentEntity"}}}}}}}, "/api/Assignment/paged": {"get": {"tags": ["Assignment"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK"}}}}, "/api/Assignment/{id}": {"get": {"tags": ["Assignment"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AssignmentEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AssignmentEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignmentEntity"}}}}}}, "put": {"tags": ["Assignment"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignmentEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignmentEntity"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AssignmentEntity"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AssignmentEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AssignmentEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignmentEntity"}}}}}}, "delete": {"tags": ["Assignment"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Assignment/composite/{version}/{name}": {"get": {"tags": ["Assignment"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AssignmentEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AssignmentEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignmentEntity"}}}}}}}, "/api/Assignment/step/{stepId}": {"get": {"tags": ["Assignment"], "parameters": [{"name": "stepId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AssignmentEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AssignmentEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AssignmentEntity"}}}}}}}}, "/api/Assignment/entity/{entityId}": {"get": {"tags": ["Assignment"], "parameters": [{"name": "entityId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AssignmentEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AssignmentEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AssignmentEntity"}}}}}}}}, "/api/Assignment/entity/{entityId}/exists": {"get": {"tags": ["Assignment"], "parameters": [{"name": "entityId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/Assignment/step/{stepId}/exists": {"get": {"tags": ["Assignment"], "parameters": [{"name": "stepId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/Assignment/version/{version}": {"get": {"tags": ["Assignment"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AssignmentEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AssignmentEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AssignmentEntity"}}}}}}}}, "/api/Assignment/name/{name}": {"get": {"tags": ["Assignment"], "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AssignmentEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AssignmentEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AssignmentEntity"}}}}}}}}, "/api/Assignment/composite": {"get": {"tags": ["Assignment"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AssignmentEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AssignmentEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignmentEntity"}}}}}}}}, "components": {"schemas": {"AssignmentEntity": {"required": ["entityIds", "name", "stepId", "version"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "version": {"maxLength": 50, "minLength": 0, "pattern": "^\\d+\\.\\d+\\.\\d+$", "type": "string"}, "name": {"maxLength": 50, "minLength": 0, "type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "nullable": true}, "updatedBy": {"type": "string", "nullable": true}, "description": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}, "isNew": {"type": "boolean", "readOnly": true}, "stepId": {"type": "string", "format": "uuid"}, "entityIds": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, "additionalProperties": false}}}}