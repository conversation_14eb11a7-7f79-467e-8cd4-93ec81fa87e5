﻿# HELP otelcol_exporter_sent_log_records Number of log record successfully sent to destination.
# TYPE otelcol_exporter_sent_log_records counter
otelcol_exporter_sent_log_records{exporter="debug",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 19636
otelcol_exporter_sent_log_records{exporter="elasticsearch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 19636
# HELP otelcol_exporter_sent_metric_points Number of metric points successfully sent to destination.
# TYPE otelcol_exporter_sent_metric_points counter
otelcol_exporter_sent_metric_points{exporter="debug",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 11264
otelcol_exporter_sent_metric_points{exporter="prometheus",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 11264
# HELP otelcol_exporter_sent_spans Number of spans successfully sent to destination.
# TYPE otelcol_exporter_sent_spans counter
otelcol_exporter_sent_spans{exporter="debug",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 2703
# HELP otelcol_process_cpu_seconds Total CPU user and system time in seconds
# TYPE otelcol_process_cpu_seconds counter
otelcol_process_cpu_seconds{service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 6.44
# HELP otelcol_process_memory_rss Total physical memory (resident set size)
# TYPE otelcol_process_memory_rss gauge
otelcol_process_memory_rss{service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 3.98966784e+08
# HELP otelcol_process_runtime_heap_alloc_bytes Bytes of allocated heap objects (see 'go doc runtime.MemStats.HeapAlloc')
# TYPE otelcol_process_runtime_heap_alloc_bytes gauge
otelcol_process_runtime_heap_alloc_bytes{service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 2.27948808e+08
# HELP otelcol_process_runtime_total_alloc_bytes Cumulative bytes allocated for heap objects (see 'go doc runtime.MemStats.TotalAlloc')
# TYPE otelcol_process_runtime_total_alloc_bytes counter
otelcol_process_runtime_total_alloc_bytes{service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 9.85611992e+08
# HELP otelcol_process_runtime_total_sys_memory_bytes Total bytes of memory obtained from the OS (see 'go doc runtime.MemStats.Sys')
# TYPE otelcol_process_runtime_total_sys_memory_bytes gauge
otelcol_process_runtime_total_sys_memory_bytes{service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 3.7777852e+08
# HELP otelcol_process_uptime Uptime of the process
# TYPE otelcol_process_uptime counter
otelcol_process_uptime{service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 620.461427814
# HELP otelcol_processor_accepted_log_records Number of log records successfully pushed into the next component in the pipeline.
# TYPE otelcol_processor_accepted_log_records counter
otelcol_processor_accepted_log_records{processor="memory_limiter",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 19636
# HELP otelcol_processor_accepted_metric_points Number of metric points successfully pushed into the next component in the pipeline.
# TYPE otelcol_processor_accepted_metric_points counter
otelcol_processor_accepted_metric_points{processor="memory_limiter",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 11264
# HELP otelcol_processor_accepted_spans Number of spans successfully pushed into the next component in the pipeline.
# TYPE otelcol_processor_accepted_spans counter
otelcol_processor_accepted_spans{processor="memory_limiter",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 2718
# HELP otelcol_processor_batch_batch_send_size Number of units in the batch
# TYPE otelcol_processor_batch_batch_send_size histogram
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="10"} 68
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="25"} 210
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="50"} 231
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="75"} 237
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="100"} 293
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="250"} 415
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="500"} 417
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="750"} 418
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="1000"} 419
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="2000"} 423
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="3000"} 423
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="4000"} 423
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="5000"} 423
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="6000"} 423
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="7000"} 423
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="8000"} 423
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="9000"} 423
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="10000"} 423
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="20000"} 423
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="30000"} 423
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="50000"} 423
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="100000"} 423
otelcol_processor_batch_batch_send_size_bucket{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",le="+Inf"} 423
otelcol_processor_batch_batch_send_size_sum{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 33603.00000000003
otelcol_processor_batch_batch_send_size_count{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 423
# HELP otelcol_processor_batch_batch_size_trigger_send Number of times the batch was sent due to a size trigger
# TYPE otelcol_processor_batch_batch_size_trigger_send counter
otelcol_processor_batch_batch_size_trigger_send{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 4
# HELP otelcol_processor_batch_timeout_trigger_send Number of times the batch was sent due to a timeout trigger
# TYPE otelcol_processor_batch_timeout_trigger_send counter
otelcol_processor_batch_timeout_trigger_send{processor="batch",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 419
# HELP otelcol_processor_dropped_log_records Number of log records that were dropped.
# TYPE otelcol_processor_dropped_log_records counter
otelcol_processor_dropped_log_records{processor="memory_limiter",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 0
# HELP otelcol_processor_dropped_metric_points Number of metric points that were dropped.
# TYPE otelcol_processor_dropped_metric_points counter
otelcol_processor_dropped_metric_points{processor="memory_limiter",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 0
# HELP otelcol_processor_dropped_spans Number of spans that were dropped.
# TYPE otelcol_processor_dropped_spans counter
otelcol_processor_dropped_spans{processor="memory_limiter",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 0
# HELP otelcol_processor_refused_log_records Number of log records that were rejected by the next component in the pipeline.
# TYPE otelcol_processor_refused_log_records counter
otelcol_processor_refused_log_records{processor="memory_limiter",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 0
# HELP otelcol_processor_refused_metric_points Number of metric points that were rejected by the next component in the pipeline.
# TYPE otelcol_processor_refused_metric_points counter
otelcol_processor_refused_metric_points{processor="memory_limiter",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 0
# HELP otelcol_processor_refused_spans Number of spans that were rejected by the next component in the pipeline.
# TYPE otelcol_processor_refused_spans counter
otelcol_processor_refused_spans{processor="memory_limiter",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0"} 0
# HELP otelcol_receiver_accepted_log_records Number of log records successfully pushed into the pipeline.
# TYPE otelcol_receiver_accepted_log_records counter
otelcol_receiver_accepted_log_records{receiver="otlp",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",transport="grpc"} 19636
# HELP otelcol_receiver_accepted_metric_points Number of metric points successfully pushed into the pipeline.
# TYPE otelcol_receiver_accepted_metric_points counter
otelcol_receiver_accepted_metric_points{receiver="otlp",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",transport="grpc"} 11264
# HELP otelcol_receiver_accepted_spans Number of spans successfully pushed into the pipeline.
# TYPE otelcol_receiver_accepted_spans counter
otelcol_receiver_accepted_spans{receiver="otlp",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",transport="grpc"} 2718
# HELP otelcol_receiver_refused_log_records Number of log records that could not be pushed into the pipeline.
# TYPE otelcol_receiver_refused_log_records counter
otelcol_receiver_refused_log_records{receiver="otlp",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",transport="grpc"} 0
# HELP otelcol_receiver_refused_metric_points Number of metric points that could not be pushed into the pipeline.
# TYPE otelcol_receiver_refused_metric_points counter
otelcol_receiver_refused_metric_points{receiver="otlp",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",transport="grpc"} 0
# HELP otelcol_receiver_refused_spans Number of spans that could not be pushed into the pipeline.
# TYPE otelcol_receiver_refused_spans counter
otelcol_receiver_refused_spans{receiver="otlp",service_instance_id="0387062e-c40e-4b16-b2cc-1db5e0797b67",service_name="otelcol-contrib",service_version="0.88.0",transport="grpc"} 0

