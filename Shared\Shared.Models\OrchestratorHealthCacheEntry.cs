using System.Text.Json.Serialization;

namespace Shared.Models;

/// <summary>
/// Represents an orchestrator health entry stored in the distributed cache.
/// Designed for orchestrator-centric health monitoring with last-writer-wins strategy.
/// Multiple orchestrator instances can report health without coordination.
/// </summary>
public class OrchestratorHealthCacheEntry
{
    /// <summary>
    /// Unique identifier of the orchestrator instance.
    /// Each orchestrator instance has its own unique ID.
    /// </summary>
    [JsonPropertyName("orchestratorId")]
    public Guid OrchestratorId { get; set; }

    /// <summary>
    /// Instance identifier for the specific orchestrator instance.
    /// Used to distinguish between multiple instances of the same orchestrator.
    /// </summary>
    [JsonPropertyName("instanceId")]
    public Guid InstanceId { get; set; }

    /// <summary>
    /// Current health status of the orchestrator.
    /// Represents the overall orchestrator health.
    /// </summary>
    [JsonPropertyName("status")]
    public HealthStatus Status { get; set; }

    /// <summary>
    /// Timestamp when this health entry was last updated.
    /// Used for last-writer-wins conflict resolution.
    /// </summary>
    [JsonPropertyName("lastUpdated")]
    public DateTime LastUpdated { get; set; }

    /// <summary>
    /// Timestamp when this entry expires and should be considered stale.
    /// Prevents stale health data from being used by other services.
    /// </summary>
    [JsonPropertyName("expiresAt")]
    public DateTime ExpiresAt { get; set; }

    /// <summary>
    /// Identifier of the pod/instance that reported this health status.
    /// Useful for debugging in distributed environments.
    /// </summary>
    [JsonPropertyName("reportingPodId")]
    public string ReportingPodId { get; set; } = Environment.MachineName;

    /// <summary>
    /// Human-readable message describing the current health status.
    /// </summary>
    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// How long the orchestrator has been running.
    /// </summary>
    [JsonPropertyName("uptime")]
    public TimeSpan Uptime { get; set; }

    /// <summary>
    /// Metadata about the orchestrator instance.
    /// </summary>
    [JsonPropertyName("metadata")]
    public OrchestratorMetadata Metadata { get; set; } = new();

    /// <summary>
    /// Performance metrics for the orchestrator.
    /// </summary>
    [JsonPropertyName("performanceMetrics")]
    public OrchestratorPerformanceMetrics PerformanceMetrics { get; set; } = new();

    /// <summary>
    /// Detailed health check results.
    /// Only included if detailed health checks are enabled.
    /// </summary>
    [JsonPropertyName("healthChecks")]
    public Dictionary<string, HealthCheckResult> HealthChecks { get; set; } = new();

    /// <summary>
    /// Orchestration-specific metrics and statistics.
    /// </summary>
    [JsonPropertyName("orchestrationMetrics")]
    public OrchestrationMetrics OrchestrationMetrics { get; set; } = new();
}

/// <summary>
/// Metadata about the orchestrator instance
/// </summary>
public class OrchestratorMetadata
{
    /// <summary>
    /// Process ID of the orchestrator
    /// </summary>
    [JsonPropertyName("processId")]
    public int ProcessId { get; set; }

    /// <summary>
    /// Hostname where the orchestrator is running
    /// </summary>
    [JsonPropertyName("hostName")]
    public string HostName { get; set; } = string.Empty;

    /// <summary>
    /// When the orchestrator was started
    /// </summary>
    [JsonPropertyName("startTime")]
    public DateTime StartTime { get; set; }

    /// <summary>
    /// Version of the orchestrator manager
    /// </summary>
    [JsonPropertyName("version")]
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// Name of the orchestrator manager
    /// </summary>
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Environment where the orchestrator is running
    /// </summary>
    [JsonPropertyName("environment")]
    public string Environment { get; set; } = string.Empty;
}

/// <summary>
/// Performance metrics for the orchestrator
/// </summary>
public class OrchestratorPerformanceMetrics
{
    /// <summary>
    /// Current CPU usage percentage (0-100)
    /// </summary>
    [JsonPropertyName("cpuUsagePercent")]
    public double CpuUsagePercent { get; set; }

    /// <summary>
    /// Current memory usage in bytes
    /// </summary>
    [JsonPropertyName("memoryUsageBytes")]
    public long MemoryUsageBytes { get; set; }

    /// <summary>
    /// Current memory usage in MB for easier reading
    /// </summary>
    [JsonPropertyName("memoryUsageMB")]
    public double MemoryUsageMB => MemoryUsageBytes / (1024.0 * 1024.0);

    /// <summary>
    /// Total number of orchestration flows started
    /// </summary>
    [JsonPropertyName("totalFlowsStarted")]
    public long TotalFlowsStarted { get; set; }

    /// <summary>
    /// Total number of orchestration flows completed successfully
    /// </summary>
    [JsonPropertyName("totalFlowsCompleted")]
    public long TotalFlowsCompleted { get; set; }

    /// <summary>
    /// Total number of orchestration flows that failed
    /// </summary>
    [JsonPropertyName("totalFlowsFailed")]
    public long TotalFlowsFailed { get; set; }

    /// <summary>
    /// Total number of step commands published
    /// </summary>
    [JsonPropertyName("totalStepCommandsPublished")]
    public long TotalStepCommandsPublished { get; set; }

    /// <summary>
    /// Total number of activity events consumed
    /// </summary>
    [JsonPropertyName("totalActivityEventsConsumed")]
    public long TotalActivityEventsConsumed { get; set; }

    /// <summary>
    /// When these metrics were collected
    /// </summary>
    [JsonPropertyName("collectedAt")]
    public DateTime CollectedAt { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Orchestration-specific metrics and statistics
/// </summary>
public class OrchestrationMetrics
{
    /// <summary>
    /// Number of currently active orchestration flows
    /// </summary>
    [JsonPropertyName("activeFlows")]
    public int ActiveFlows { get; set; }

    /// <summary>
    /// Number of currently active step executions
    /// </summary>
    [JsonPropertyName("activeStepExecutions")]
    public int ActiveStepExecutions { get; set; }

    /// <summary>
    /// Number of active cache entries
    /// </summary>
    [JsonPropertyName("activeCacheEntries")]
    public long ActiveCacheEntries { get; set; }

    /// <summary>
    /// Average age of cache entries in seconds
    /// </summary>
    [JsonPropertyName("averageCacheEntryAge")]
    public double AverageCacheEntryAge { get; set; }

    /// <summary>
    /// Current orchestration throughput (flows per minute)
    /// </summary>
    [JsonPropertyName("throughputFlowsPerMinute")]
    public double ThroughputFlowsPerMinute { get; set; }

    /// <summary>
    /// Average flow execution time in seconds
    /// </summary>
    [JsonPropertyName("averageFlowExecutionTime")]
    public double AverageFlowExecutionTime { get; set; }
}
