{"openapi": "3.0.1", "info": {"title": "Manager.OrchestratedFlow", "version": "1.0"}, "paths": {"/api/OrchestratedFlow": {"get": {"tags": ["OrchestratedFlow"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}}}}}, "post": {"tags": ["OrchestratedFlow"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}}}}}, "/api/OrchestratedFlow/paged": {"get": {"tags": ["OrchestratedFlow"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK"}}}}, "/api/OrchestratedFlow/{id}": {"get": {"tags": ["OrchestratedFlow"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}}}}, "put": {"tags": ["OrchestratedFlow"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}}}}, "delete": {"tags": ["OrchestratedFlow"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/OrchestratedFlow/composite/{version}/{name}": {"get": {"tags": ["OrchestratedFlow"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}}}}}, "/api/OrchestratedFlow/workflow/{workflowId}": {"get": {"tags": ["OrchestratedFlow"], "parameters": [{"name": "workflowId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}}}}}}, "/api/OrchestratedFlow/assignment/{assignmentId}": {"get": {"tags": ["OrchestratedFlow"], "parameters": [{"name": "assignmentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}}}}}}, "/api/OrchestratedFlow/version/{version}": {"get": {"tags": ["OrchestratedFlow"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}}}}}}, "/api/OrchestratedFlow/name/{name}": {"get": {"tags": ["OrchestratedFlow"], "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}}}}}}, "/api/OrchestratedFlow/workflow/{workflowId}/exists": {"get": {"tags": ["OrchestratedFlow"], "parameters": [{"name": "workflowId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/OrchestratedFlow/assignment/{assignmentId}/exists": {"get": {"tags": ["OrchestratedFlow"], "parameters": [{"name": "assignmentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/OrchestratedFlow/composite": {"get": {"tags": ["OrchestratedFlow"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OrchestratedFlowEntity"}}}}}}}}, "components": {"schemas": {"OrchestratedFlowEntity": {"required": ["name", "version", "workflowId"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "version": {"maxLength": 50, "minLength": 0, "pattern": "^\\d+\\.\\d+\\.\\d+$", "type": "string"}, "name": {"maxLength": 50, "minLength": 0, "type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "nullable": true}, "updatedBy": {"type": "string", "nullable": true}, "description": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}, "isNew": {"type": "boolean", "readOnly": true}, "workflowId": {"type": "string", "format": "uuid"}, "assignmentIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "cronExpression": {"type": "string", "nullable": true}, "isScheduleEnabled": {"type": "boolean"}}, "additionalProperties": false}}}}