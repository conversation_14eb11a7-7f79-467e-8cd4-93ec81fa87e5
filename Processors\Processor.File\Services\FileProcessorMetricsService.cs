using System.Diagnostics.Metrics;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Shared.Correlation;
using Shared.Processor.Models;
using Shared.Processor.Services;

namespace Processor.File.Services;

/// <summary>
/// Concrete metrics service for FileProcessor-specific metrics.
/// Extends the base processor metrics with file processing specific measurements.
/// Uses consistent labeling from appsettings configuration.
/// </summary>
public class FileProcessorMetricsService : IDisposable
{
    private readonly ProcessorConfiguration _config;
    private readonly ILogger<FileProcessorMetricsService> _logger;
    private readonly Meter _meter;

    // File Processing Specific Metrics
    private readonly Counter<long> _filesProcessedCounter;
    private readonly Counter<long> _filesSuccessfulCounter;
    private readonly Counter<long> _filesFailedCounter;
    private readonly Histogram<double> _fileProcessingDurationHistogram;
    private readonly Histogram<long> _fileSizeHistogram;
    private readonly Gauge<long> _currentFileQueueSizeGauge;
    private readonly Counter<long> _fileValidationErrorsCounter;
    private readonly Gauge<double> _averageFileSizeGauge;

    // Entity Processing Metrics
    private readonly Counter<long> _entitiesExtractedCounter;
    private readonly Histogram<double> _entityExtractionTimeHistogram;
    private readonly Counter<long> _entityValidationErrorsCounter;

    private readonly IProcessorMetricsLabelService _labelService;

    public FileProcessorMetricsService(
        IOptions<ProcessorConfiguration> config,
        IConfiguration configuration,
        ILogger<FileProcessorMetricsService> logger,
        IProcessorMetricsLabelService labelService)
    {
        _config = config.Value;
        _logger = logger;
        _labelService = labelService;

        // Use the recommended unique meter name pattern: {Version}_{Name}
        var meterName = $"{_config.Version}_{_config.Name}";
        _meter = new Meter($"{meterName}.Metrics");

        // Initialize file processing metrics
        _filesProcessedCounter = _meter.CreateCounter<long>(
            "processor_files_processed_total",
            "Total number of files processed by the processor");

        _filesSuccessfulCounter = _meter.CreateCounter<long>(
            "processor_files_successful_total",
            "Total number of files successfully processed");

        _filesFailedCounter = _meter.CreateCounter<long>(
            "processor_files_failed_total",
            "Total number of files that failed processing");

        _fileProcessingDurationHistogram = _meter.CreateHistogram<double>(
            "processor_file_processing_duration_seconds",
            "Duration of file processing operations in seconds");

        _fileSizeHistogram = _meter.CreateHistogram<long>(
            "processor_file_size_bytes",
            "Size of processed files in bytes");

        _currentFileQueueSizeGauge = _meter.CreateGauge<long>(
            "processor_queue_size",
            "Current number of files waiting to be processed");

        _fileValidationErrorsCounter = _meter.CreateCounter<long>(
            "processor_validation_errors_total",
            "Total number of file validation errors");

        _averageFileSizeGauge = _meter.CreateGauge<double>(
            "processor_average_file_size_bytes",
            "Average size of processed files in bytes");

        // Initialize entity processing metrics
        _entitiesExtractedCounter = _meter.CreateCounter<long>(
            "processor_entities_extracted_total",
            "Total number of entities extracted from files");

        _entityExtractionTimeHistogram = _meter.CreateHistogram<double>(
            "processor_entity_extraction_duration_seconds",
            "Duration of entity extraction operations in seconds");

        _entityValidationErrorsCounter = _meter.CreateCounter<long>(
            "processor_entity_validation_errors_total",
            "Total number of entity validation errors");
    }



    /// <summary>
    /// Records file processing completion metrics.
    /// </summary>
    public void RecordFileProcessed(bool success, TimeSpan duration, long fileSizeBytes, int entitiesExtracted, string? fileType = null)
    {
        // Get correlation ID from current context
        var correlationId = GetCurrentCorrelationId();
        var tags = _labelService.GetFileLabels(correlationId, fileType ?? "unknown");
        return;
        _filesProcessedCounter.Add(1, tags);

        if (success)
        {
            _filesSuccessfulCounter.Add(1, tags);
        }
        else
        {
            _filesFailedCounter.Add(1, tags);
        }

        _fileProcessingDurationHistogram.Record(duration.TotalSeconds, tags);
        _fileSizeHistogram.Record(fileSizeBytes, tags);
        _entitiesExtractedCounter.Add(entitiesExtracted, tags);

        _logger.LogDebugWithCorrelation(
            "Recorded file processing metrics: Success={Success}, Duration={Duration}ms, Size={Size}bytes, Entities={Entities}",
            success, duration.TotalMilliseconds, fileSizeBytes, entitiesExtracted);
    }

    /// <summary>
    /// Records entity extraction metrics.
    /// </summary>
    public void RecordEntityExtraction(int entitiesCount, TimeSpan extractionTime, string entityType)
    {
        var correlationId = GetCurrentCorrelationId();
        var tags = _labelService.GetFileLabels(correlationId, entityType);

        _entitiesExtractedCounter.Add(entitiesCount, tags);
        _entityExtractionTimeHistogram.Record(extractionTime.TotalSeconds, tags);
    }

    /// <summary>
    /// Records file validation error.
    /// </summary>
    public void RecordFileValidationError(string errorType, string? fileType = null)
    {
        var correlationId = GetCurrentCorrelationId();
        var tags = _labelService.GetFileLabels(correlationId, fileType ?? "unknown").ToList();
        tags.Add(new KeyValuePair<string, object?>("error_type", errorType));

        _fileValidationErrorsCounter.Add(1, tags.ToArray());
    }

    /// <summary>
    /// Records entity validation error.
    /// </summary>
    public void RecordEntityValidationError(string errorType, string entityType)
    {
        var correlationId = GetCurrentCorrelationId();
        var tags = _labelService.GetFileLabels(correlationId, entityType).ToList();
        tags.Add(new KeyValuePair<string, object?>("error_type", errorType));

        _entityValidationErrorsCounter.Add(1, tags.ToArray());
    }

    /// <summary>
    /// Updates the current file queue size.
    /// </summary>
    public void UpdateFileQueueSize(long queueSize)
    {
        var tags = _labelService.GetSystemLabels();
        _currentFileQueueSizeGauge.Record(queueSize, tags);
    }

    /// <summary>
    /// Updates the average file size metric.
    /// </summary>
    public void UpdateAverageFileSize(double averageSizeBytes)
    {
        var tags = _labelService.GetSystemLabels();
        _averageFileSizeGauge.Record(averageSizeBytes, tags);
    }

    /// <summary>
    /// Gets the current correlation ID from the activity context
    /// </summary>
    private string GetCurrentCorrelationId()
    {
        var activity = System.Diagnostics.Activity.Current;
        if (activity?.GetBaggageItem("correlation.id") is string baggageValue &&
            Guid.TryParse(baggageValue, out var correlationId))
        {
            return correlationId.ToString();
        }

        return Guid.Empty.ToString();
    }

    public void Dispose()
    {
        _meter?.Dispose();
    }
}
