<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>Shared.MongoDB</RootNamespace>
    <AssemblyName>Shared.MongoDB</AssemblyName>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
    <Authors>Manager.Schemas Team</Authors>
    <Description>Shared MongoDB configuration and serialization classes for the Manager.Schemas application</Description>
    <PackageId>Shared.MongoDB</PackageId>
    <Version>1.0.0</Version>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MongoDB.Driver" Version="2.22.0" />
    <PackageReference Include="MongoDB.Bson" Version="2.22.0" />
    <PackageReference Include="System.Text.Json" Version="9.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Shared.Entities\Shared.Entities.csproj" />
  </ItemGroup>

</Project>
