<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>Shared.MassTransit</RootNamespace>
    <AssemblyName>Shared.MassTransit</AssemblyName>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
    <Authors>Manager.Schemas Team</Authors>
    <Description>Shared MassTransit components for the Manager.Schemas application</Description>
    <PackageId>Shared.MassTransit</PackageId>
    <Version>1.0.0</Version>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="MassTransit" Version="8.2.5" />
    <PackageReference Include="MassTransit.RabbitMQ" Version="8.2.5" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.0" />
  </ItemGroup>

  <!-- Project references -->
  <ItemGroup>
    <ProjectReference Include="..\Shared.Correlation\Shared.Correlation.csproj" />
    <ProjectReference Include="..\Shared.Entities\Shared.Entities.csproj" />
    <ProjectReference Include="..\Shared.Models\Shared.Models.csproj" />
  </ItemGroup>

</Project>
