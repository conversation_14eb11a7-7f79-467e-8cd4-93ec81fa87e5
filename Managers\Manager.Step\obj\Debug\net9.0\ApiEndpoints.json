[{"ContainingType": "Manager.Step.Controllers.StepController", "Method": "GetAll", "RelativePath": "api/Step", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.StepEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Step.Controllers.StepController", "Method": "Create", "RelativePath": "api/Step", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "entity", "Type": "Shared.Entities.StepEntity", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Entities.StepEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Step.Controllers.StepController", "Method": "GetById", "RelativePath": "api/Step/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Entities.StepEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Step.Controllers.StepController", "Method": "Update", "RelativePath": "api/Step/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "entity", "Type": "Shared.Entities.StepEntity", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Entities.StepEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Step.Controllers.StepController", "Method": "Delete", "RelativePath": "api/Step/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Manager.Step.Controllers.StepController", "Method": "CheckStepExists", "RelativePath": "api/Step/{stepId}/exists", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "stepId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Step.Controllers.StepController", "Method": "GetByCompositeKeyEmpty", "RelativePath": "api/Step/composite", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Shared.Entities.StepEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Step.Controllers.StepController", "Method": "GetByCompositeKey", "RelativePath": "api/Step/composite/{version}/{name}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "version", "Type": "System.String", "IsRequired": true}, {"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Entities.StepEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Step.Controllers.StepController", "Method": "GetByEntryCondition", "RelativePath": "api/Step/entrycondition/{condition}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "condition", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.StepEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Step.Controllers.StepController", "Method": "GetByName", "RelativePath": "api/Step/name/{name}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.StepEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Step.Controllers.StepController", "Method": "GetByNextStepId", "RelativePath": "api/Step/nextstep/{stepId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "stepId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.StepEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Step.Controllers.StepController", "Method": "GetPaged", "RelativePath": "api/Step/paged", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Manager.Step.Controllers.StepController", "Method": "GetByProcessorId", "RelativePath": "api/Step/processor/{processorId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "processorId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.StepEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Step.Controllers.StepController", "Method": "CheckProcessorReferences", "RelativePath": "api/Step/processor/{processorId}/exists", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "processorId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.Step.Controllers.StepController", "Method": "GetByVersion", "RelativePath": "api/Step/version/{version}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "version", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.StepEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]