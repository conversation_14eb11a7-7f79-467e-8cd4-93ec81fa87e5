{"openapi": "3.0.1", "info": {"title": "Manager.Delivery", "version": "1.0"}, "paths": {"/api/Delivery": {"get": {"tags": ["Delivery"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryEntity"}}}}}}}, "post": {"tags": ["Delivery"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliveryEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeliveryEntity"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeliveryEntity"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DeliveryEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeliveryEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeliveryEntity"}}}}}}}, "/api/Delivery/paged": {"get": {"tags": ["Delivery"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK"}}}}, "/api/Delivery/{id}": {"get": {"tags": ["Delivery"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DeliveryEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeliveryEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeliveryEntity"}}}}}}, "put": {"tags": ["Delivery"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeliveryEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeliveryEntity"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/DeliveryEntity"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DeliveryEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeliveryEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeliveryEntity"}}}}}}, "delete": {"tags": ["Delivery"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Delivery/composite/{version}/{name}": {"get": {"tags": ["Delivery"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DeliveryEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeliveryEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeliveryEntity"}}}}}}}, "/api/Delivery/payload/{payload}": {"get": {"tags": ["Delivery"], "parameters": [{"name": "payload", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryEntity"}}}}}}}}, "/api/Delivery/schemaId/{schemaId}": {"get": {"tags": ["Delivery"], "parameters": [{"name": "schemaId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryEntity"}}}}}}}}, "/api/Delivery/version/{version}": {"get": {"tags": ["Delivery"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryEntity"}}}}}}}}, "/api/Delivery/name/{name}": {"get": {"tags": ["Delivery"], "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DeliveryEntity"}}}}}}}}, "/api/Delivery/composite": {"get": {"tags": ["Delivery"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DeliveryEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DeliveryEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DeliveryEntity"}}}}}}}, "/api/Delivery/schema/{schemaId}/exists": {"get": {"tags": ["Delivery"], "parameters": [{"name": "schemaId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}}, "components": {"schemas": {"DeliveryEntity": {"required": ["name", "payload", "schemaId", "version"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "version": {"maxLength": 50, "minLength": 0, "pattern": "^\\d+\\.\\d+\\.\\d+$", "type": "string"}, "name": {"maxLength": 50, "minLength": 0, "type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "nullable": true}, "updatedBy": {"type": "string", "nullable": true}, "description": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}, "isNew": {"type": "boolean", "readOnly": true}, "payload": {"minLength": 1, "type": "string"}, "schemaId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}}}}