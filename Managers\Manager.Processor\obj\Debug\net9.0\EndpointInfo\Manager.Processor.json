{"openapi": "3.0.1", "info": {"title": "Manager.Processor", "version": "1.0"}, "paths": {"/api/Processor": {"get": {"tags": ["Processor"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessorEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessorEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessorEntity"}}}}}}}, "post": {"tags": ["Processor"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessorEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProcessorEntity"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProcessorEntity"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProcessorEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProcessorEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProcessorEntity"}}}}}}}, "/api/Processor/paged": {"get": {"tags": ["Processor"], "parameters": [{"name": "page", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK"}}}}, "/api/Processor/{id}": {"get": {"tags": ["Processor"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProcessorEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProcessorEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProcessorEntity"}}}}}}, "put": {"tags": ["Processor"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessorEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProcessorEntity"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProcessorEntity"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProcessorEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProcessorEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProcessorEntity"}}}}}}, "delete": {"tags": ["Processor"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Processor/composite/{version}/{name}": {"get": {"tags": ["Processor"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProcessorEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProcessorEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProcessorEntity"}}}}}}}, "/api/Processor/input-schema/{inputSchemaId}": {"get": {"tags": ["Processor"], "parameters": [{"name": "inputSchemaId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessorEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessorEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessorEntity"}}}}}}}}, "/api/Processor/output-schema/{outputSchemaId}": {"get": {"tags": ["Processor"], "parameters": [{"name": "outputSchemaId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessorEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessorEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessorEntity"}}}}}}}}, "/api/Processor/version/{version}": {"get": {"tags": ["Processor"], "parameters": [{"name": "version", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessorEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessorEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessorEntity"}}}}}}}}, "/api/Processor/name/{name}": {"get": {"tags": ["Processor"], "parameters": [{"name": "name", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessorEntity"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessorEntity"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessorEntity"}}}}}}}}, "/api/Processor/composite": {"get": {"tags": ["Processor"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProcessorEntity"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProcessorEntity"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProcessorEntity"}}}}}}}, "/api/Processor/input-schema/{schemaId}/exists": {"get": {"tags": ["Processor"], "parameters": [{"name": "schemaId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/Processor/output-schema/{schemaId}/exists": {"get": {"tags": ["Processor"], "parameters": [{"name": "schemaId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/Processor/{processorId}/exists": {"get": {"tags": ["Processor"], "parameters": [{"name": "processorId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}}, "components": {"schemas": {"ProcessorEntity": {"required": ["inputSchemaId", "name", "outputSchemaId", "version"], "type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "version": {"maxLength": 50, "minLength": 0, "pattern": "^\\d+\\.\\d+\\.\\d+$", "type": "string"}, "name": {"maxLength": 50, "minLength": 0, "type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "nullable": true}, "updatedBy": {"type": "string", "nullable": true}, "description": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}, "isNew": {"type": "boolean", "readOnly": true}, "inputSchemaId": {"type": "string", "format": "uuid"}, "outputSchemaId": {"type": "string", "format": "uuid"}, "implementationHash": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}