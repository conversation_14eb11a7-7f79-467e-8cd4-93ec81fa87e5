[{"ContainingType": "Manager.OrchestratedFlow.Controllers.OrchestratedFlowController", "Method": "GetAll", "RelativePath": "api/OrchestratedFlow", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.OrchestratedFlowEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.OrchestratedFlow.Controllers.OrchestratedFlowController", "Method": "Create", "RelativePath": "api/OrchestratedFlow", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "entity", "Type": "Shared.Entities.OrchestratedFlowEntity", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Entities.OrchestratedFlowEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.OrchestratedFlow.Controllers.OrchestratedFlowController", "Method": "GetById", "RelativePath": "api/OrchestratedFlow/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Entities.OrchestratedFlowEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.OrchestratedFlow.Controllers.OrchestratedFlowController", "Method": "Update", "RelativePath": "api/OrchestratedFlow/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "entity", "Type": "Shared.Entities.OrchestratedFlowEntity", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Entities.OrchestratedFlowEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.OrchestratedFlow.Controllers.OrchestratedFlowController", "Method": "Delete", "RelativePath": "api/OrchestratedFlow/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Manager.OrchestratedFlow.Controllers.OrchestratedFlowController", "Method": "GetByAssignmentId", "RelativePath": "api/OrchestratedFlow/assignment/{assignmentId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "assignmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.OrchestratedFlowEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.OrchestratedFlow.Controllers.OrchestratedFlowController", "Method": "CheckAssignmentReferences", "RelativePath": "api/OrchestratedFlow/assignment/{assignmentId}/exists", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "assignmentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.OrchestratedFlow.Controllers.OrchestratedFlowController", "Method": "GetByCompositeKeyEmpty", "RelativePath": "api/OrchestratedFlow/composite", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Shared.Entities.OrchestratedFlowEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.OrchestratedFlow.Controllers.OrchestratedFlowController", "Method": "GetByCompositeKey", "RelativePath": "api/OrchestratedFlow/composite/{version}/{name}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "version", "Type": "System.String", "IsRequired": true}, {"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "Shared.Entities.OrchestratedFlowEntity", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.OrchestratedFlow.Controllers.OrchestratedFlowController", "Method": "GetByName", "RelativePath": "api/OrchestratedFlow/name/{name}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "name", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.OrchestratedFlowEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.OrchestratedFlow.Controllers.OrchestratedFlowController", "Method": "GetPaged", "RelativePath": "api/OrchestratedFlow/paged", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Manager.OrchestratedFlow.Controllers.OrchestratedFlowController", "Method": "GetByVersion", "RelativePath": "api/OrchestratedFlow/version/{version}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "version", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.OrchestratedFlowEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.OrchestratedFlow.Controllers.OrchestratedFlowController", "Method": "GetByWorkflowId", "RelativePath": "api/OrchestratedFlow/workflow/{workflowId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflowId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Shared.Entities.OrchestratedFlowEntity, Shared.Entities, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Manager.OrchestratedFlow.Controllers.OrchestratedFlowController", "Method": "CheckWorkflowReferences", "RelativePath": "api/OrchestratedFlow/workflow/{workflowId}/exists", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "workflowId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}]