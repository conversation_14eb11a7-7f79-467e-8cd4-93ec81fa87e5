﻿{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "MassTransit": "Information",
      "MongoDB": "Information"
    }
  },
  "AllowedHosts": "*",
  "ManagerConfiguration": {
    "Version": "1.0.0",
    "Name": "OrchestratedFlowManager",
    "Description": "Manager for orchestrated flow entities and operations"
  },
  "ConnectionStrings": {
    "MongoDB": "mongodb://localhost:27017"
  },
  "MongoDB": {
    "DatabaseName": "ManagerOrchestratedFlowDB"
  },
  "RabbitMQ": {
    "Host": "localhost",
    "VirtualHost": "/",
    "Username": "guest",
    "Password": "guest"
  },
  "OpenTelemetry": {
    "Endpoint": "http://localhost:4317",
    "UseConsoleInDevelopment": false,
    "ServiceName": "OrchestratedFlowManager",
    "ServiceVersion": "1.0.0"
  },
  "Services": {
    "WorkflowManager": {
      "BaseUrl": "http://localhost:5030"
    },
    "AssignmentManager": {
      "BaseUrl": "http://localhost:5010"
    }
  },
  "Features": {
    "ReferentialIntegrityValidation": true
  },
  "ReferentialIntegrity": {
    "ValidationTimeoutMs": 3000,
    "EnableParallelValidation": true,
    "ValidateSourceReferences": true,
    "ValidateDestinationReferences": true,
    "ValidateAssignmentReferences": true,
    "ValidateScheduledFlowReferences": true,
    "ValidateStepReferences": true,
    "ValidateFlowReferences": true
  }
}
