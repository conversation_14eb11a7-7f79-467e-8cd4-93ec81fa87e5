<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Shared.Services</name>
    </assembly>
    <members>
        <member name="T:Shared.Services.CacheService">
            <summary>
            Hazelcast-specific cache service implementation
            </summary>
        </member>
        <member name="T:Shared.Services.ICacheService">
            <summary>
            Interface for cache service operations
            </summary>
        </member>
        <member name="M:Shared.Services.ICacheService.GetAsync(System.String,System.String)">
            <summary>
            Retrieves data from cache
            </summary>
            <param name="mapName">Name of the cache map</param>
            <param name="key">Cache key</param>
            <returns>Cached data or null if not found</returns>
        </member>
        <member name="M:Shared.Services.ICacheService.SetAsync(System.String,System.String,System.String)">
            <summary>
            Stores data in cache
            </summary>
            <param name="mapName">Name of the cache map</param>
            <param name="key">Cache key</param>
            <param name="value">Data to store</param>
            <returns>Task representing the operation</returns>
        </member>
        <member name="M:Shared.Services.ICacheService.SetAsync(System.String,System.String,System.String,System.TimeSpan)">
            <summary>
            Stores data in cache with time-to-live (TTL)
            </summary>
            <param name="mapName">Name of the cache map</param>
            <param name="key">Cache key</param>
            <param name="value">Data to store</param>
            <param name="ttl">Time-to-live for the cache entry</param>
            <returns>Task representing the operation</returns>
        </member>
        <member name="M:Shared.Services.ICacheService.ExistsAsync(System.String,System.String)">
            <summary>
            Checks if a key exists in cache
            </summary>
            <param name="mapName">Name of the cache map</param>
            <param name="key">Cache key</param>
            <returns>True if key exists, false otherwise</returns>
        </member>
        <member name="M:Shared.Services.ICacheService.RemoveAsync(System.String,System.String)">
            <summary>
            Removes data from cache
            </summary>
            <param name="mapName">Name of the cache map</param>
            <param name="key">Cache key</param>
            <returns>Task representing the operation</returns>
        </member>
        <member name="M:Shared.Services.ICacheService.IsHealthyAsync">
            <summary>
            Checks if the cache service is healthy and accessible
            </summary>
            <returns>True if healthy, false otherwise</returns>
        </member>
        <member name="M:Shared.Services.ICacheService.GetCacheStatisticsAsync(System.String)">
            <summary>
            Gets cache statistics for a specific map
            </summary>
            <param name="mapName">Name of the cache map</param>
            <returns>Tuple containing entry count and average age in seconds</returns>
        </member>
        <member name="M:Shared.Services.ICacheService.GetCacheKey(System.Guid,System.Guid,System.Guid,System.Guid)">
            <summary>
            Generates a standardized cache key for orchestration workflow data using the pattern:
            {orchestratedFlowEntityId}:{stepId}:{executionId}:{correlationId}
            This ensures consistent key formatting across all processors and orchestration components.
            </summary>
        </member>
        <member name="T:Shared.Services.IManagerMetricsService">
            <summary>
            Interface for manager-specific metrics service.
            Provides comprehensive manager operation metrics for analysis and monitoring.
            </summary>
        </member>
        <member name="M:Shared.Services.IManagerMetricsService.RecordRequestProcessed(System.Boolean,System.TimeSpan,System.String,System.String)">
            <summary>
            Records request processing completion metrics.
            </summary>
            <param name="success">Whether the request was successful</param>
            <param name="duration">Duration of the request processing</param>
            <param name="operation">The operation type (e.g., "create", "update", "delete", "get")</param>
            <param name="entityType">The entity type being operated on (optional)</param>
        </member>
        <member name="M:Shared.Services.IManagerMetricsService.RecordEntityOperation(System.String,System.String,System.Int32)">
            <summary>
            Records entity operation metrics.
            </summary>
            <param name="operation">The operation type (e.g., "create", "update", "delete", "query")</param>
            <param name="entityType">The entity type being operated on</param>
            <param name="count">Number of entities affected (default: 1)</param>
        </member>
        <member name="M:Shared.Services.IManagerMetricsService.RecordValidation(System.Boolean,System.TimeSpan,System.String)">
            <summary>
            Records validation metrics.
            </summary>
            <param name="success">Whether the validation was successful</param>
            <param name="duration">Duration of the validation operation</param>
            <param name="validationType">The type of validation performed</param>
        </member>
        <member name="M:Shared.Services.IManagerMetricsService.RecordHealthStatus(System.Int32)">
            <summary>
            Records health status.
            </summary>
            <param name="status">Health status (0=Healthy, 1=Degraded, 2=Unhealthy)</param>
        </member>
        <member name="M:Shared.Services.IManagerMetricsService.RecordOperation(System.String,System.String,System.TimeSpan,System.Boolean)">
            <summary>
            Records a generic operation with timing and success status.
            </summary>
            <param name="operation">The operation type</param>
            <param name="entityType">The entity type being operated on</param>
            <param name="duration">Duration of the operation</param>
            <param name="success">Whether the operation was successful</param>
        </member>
        <member name="T:Shared.Services.ManagerMetricsService">
            <summary>
            Service for exposing manager-specific metrics using OpenTelemetry.
            Provides comprehensive manager operation metrics for analysis and monitoring.
            Uses consistent labeling from appsettings configuration (Name and Version).
            </summary>
        </member>
        <member name="M:Shared.Services.ManagerMetricsService.RecordRequestProcessed(System.Boolean,System.TimeSpan,System.String,System.String)">
            <summary>
            Records request processing completion metrics.
            </summary>
        </member>
        <member name="M:Shared.Services.ManagerMetricsService.RecordEntityOperation(System.String,System.String,System.Int32)">
            <summary>
            Records entity operation metrics.
            </summary>
        </member>
        <member name="M:Shared.Services.ManagerMetricsService.RecordValidation(System.Boolean,System.TimeSpan,System.String)">
            <summary>
            Records validation metrics.
            </summary>
        </member>
        <member name="M:Shared.Services.ManagerMetricsService.RecordHealthStatus(System.Int32)">
            <summary>
            Records health status.
            </summary>
        </member>
        <member name="M:Shared.Services.ManagerMetricsService.RecordOperation(System.String,System.String,System.TimeSpan,System.Boolean)">
            <summary>
            Records a generic operation with timing and success status.
            </summary>
        </member>
        <member name="M:Shared.Services.ManagerMetricsService.CreateManagerTags(System.ValueTuple{System.String,System.Object}[])">
            <summary>
            Creates consistent manager tags using configuration values.
            Uses Name and Version from ManagerConfiguration for consistent labeling.
            </summary>
        </member>
    </members>
</doc>
